# Image Upload System Setup & Testing Guide

## Overview
This guide provides complete setup instructions and testing procedures for the new image upload system implemented across the admin dashboard.

## 🚀 Quick Setup

### 1. Database Migration
Run the storage setup migration to create buckets and policies:

```sql
-- Apply the migration file
-- This should be run automatically if using Supabase CLI
-- Or manually execute the contents of: supabase/migrations/20250719070000-setup-storage-buckets.sql
```

### 2. Supabase Dashboard Configuration

#### Enable Storage in Supabase Dashboard:
1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the left sidebar
3. If storage is not enabled, click **Enable Storage**

#### Verify Buckets Creation:
The migration should create these buckets automatically:
- `product-images` (10MB limit, public)
- `portfolio-images` (10MB limit, public) 
- `artist-images` (10MB limit, public)
- `homepage-images` (10MB limit, public)

#### Manual Bucket Creation (if needed):
If buckets weren't created automatically:

1. Go to **Storage** → **Buckets**
2. Click **New Bucket** for each:
   - **Name**: `product-images`
   - **Public**: ✅ Enabled
   - **File size limit**: 10 MB
   - **Allowed MIME types**: `image/jpeg,image/png,image/webp,image/gif`
3. Repeat for `portfolio-images`, `artist-images`, `homepage-images`

### 3. RLS Policies Verification
The migration creates RLS policies automatically. Verify in **Authentication** → **Policies**:

**For each bucket, you should see:**
- Public read access policy
- Authenticated upload policy  
- Authenticated update/delete policies

## 📋 Complete SQL Setup Commands

If you need to run the setup manually, execute these commands in your Supabase SQL editor:

```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('product-images', 'product-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('portfolio-images', 'portfolio-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('artist-images', 'artist-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('homepage-images', 'homepage-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;

-- Create public read policies
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT USING (bucket_id = 'product-images');

CREATE POLICY "Public read access for portfolio images" ON storage.objects
  FOR SELECT USING (bucket_id = 'portfolio-images');

CREATE POLICY "Public read access for artist images" ON storage.objects
  FOR SELECT USING (bucket_id = 'artist-images');

CREATE POLICY "Public read access for homepage images" ON storage.objects
  FOR SELECT USING (bucket_id = 'homepage-images');

-- Create authenticated upload policies
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'product-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can upload portfolio images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'portfolio-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can upload artist images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'artist-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can upload homepage images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'homepage-images' AND auth.role() = 'authenticated');

-- Create update/delete policies for authenticated users
CREATE POLICY "Authenticated users can update product images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'product-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete product images" ON storage.objects
  FOR DELETE USING (bucket_id = 'product-images' AND auth.role() = 'authenticated');

-- Repeat update/delete policies for other buckets...
-- (See full migration file for complete policies)
```

## 🧪 Comprehensive Testing Guide

### Prerequisites
1. Admin user account created and logged in
2. Storage buckets configured
3. RLS policies in place
4. Application running locally

### Test Scenarios

#### 1. Product Image Upload Testing

**Access**: Navigate to Admin → Products → Add Product

**Test Cases:**

**✅ Successful Upload**
1. Click "Select Image" or drag image to upload area
2. Select a valid image file (JPEG, PNG, WebP, GIF under 10MB)
3. Verify:
   - Progress bar shows during upload
   - Success message appears
   - Image preview displays correctly
   - Image URL is populated in form data

**❌ File Validation Testing**
1. Try uploading invalid file types (.txt, .pdf, .doc)
   - Expected: Error message "Please upload a valid image file"
2. Try uploading oversized file (>10MB)
   - Expected: Error message "File size must be less than 10MB"

**🔄 Replace/Remove Testing**
1. Upload an image successfully
2. Hover over image preview
3. Click "Replace" - should allow new upload
4. Click "Remove" - should clear image and show upload area

#### 2. Portfolio Image Upload Testing

**Access**: Navigate to Admin → Portfolio → Add Portfolio Item

**Test Cases:**
- Same validation tests as products
- Verify landscape aspect ratio preview
- Test drag-and-drop functionality
- Verify image compression works for large files

#### 3. Artist Profile Image Testing

**Access**: Navigate to Admin → Artist

**Test Cases:**
- Test portrait image upload (square aspect ratio)
- Test hero image upload (landscape aspect ratio)
- Verify both images can be uploaded simultaneously
- Test form saves with uploaded images

#### 4. Homepage Image Testing

**Access**: Navigate to Admin → Homepage

**Test Cases:**
- Test hero background image upload
- Test about section image upload
- Verify different aspect ratios work correctly
- Test form saves and images persist

### 5. Integration Testing

**Frontend Display Verification:**
1. Upload images in admin
2. Navigate to public pages
3. Verify images display correctly:
   - Product pages show product images
   - Portfolio page shows portfolio images
   - Homepage shows hero/about images
   - Artist section shows artist images

**Database Verification:**
```sql
-- Check uploaded files in storage
SELECT * FROM storage.objects WHERE bucket_id IN ('product-images', 'portfolio-images', 'artist-images', 'homepage-images');

-- Check database records have correct URLs
SELECT name, image_url FROM products WHERE image_url IS NOT NULL;
SELECT title, image_url FROM portfolio_items WHERE image_url IS NOT NULL;
SELECT name, portrait_url, hero_image_url FROM artist_profile;
```

### 6. Error Handling Testing

**Network Issues:**
1. Disconnect internet during upload
2. Verify error message displays
3. Reconnect and retry upload

**Authentication Issues:**
1. Log out during upload process
2. Verify appropriate error handling

**Storage Issues:**
1. Temporarily disable storage bucket
2. Verify graceful error handling

## 🔧 Troubleshooting

### Common Issues

**"Failed to upload image" Error:**
- Check Supabase storage is enabled
- Verify bucket exists and is public
- Check RLS policies are correctly set
- Ensure user is authenticated

**Images not displaying on public pages:**
- Verify bucket is set to public
- Check image URLs in database are correct
- Verify public read policies exist

**Upload progress stuck:**
- Check network connection
- Verify file size is under limit
- Check browser console for errors

### Debug Commands

```sql
-- Check bucket configuration
SELECT * FROM storage.buckets WHERE id LIKE '%images';

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';

-- Check uploaded files
SELECT bucket_id, name, created_at FROM storage.objects ORDER BY created_at DESC LIMIT 10;
```

## 📊 Performance Considerations

**Image Compression:**
- Images are automatically compressed to max 1920px width
- Quality set to 80% for optimal balance
- Original aspect ratio maintained

**File Naming:**
- Unique filenames generated with timestamp and random component
- Prevents conflicts and caching issues
- Format: `original-name-timestamp-random.ext`

**Storage Limits:**
- 10MB per file limit
- Adjust in bucket settings if needed
- Monitor storage usage in Supabase dashboard

## ✅ Success Criteria

The image upload system is working correctly when:

1. ✅ All admin forms use ImageUpload component instead of text inputs
2. ✅ Images upload successfully with progress indication
3. ✅ File validation prevents invalid uploads
4. ✅ Image compression reduces file sizes appropriately
5. ✅ Uploaded images display correctly in admin preview
6. ✅ Images appear correctly on public-facing pages
7. ✅ Replace/remove functionality works properly
8. ✅ Error handling provides clear user feedback
9. ✅ Database records contain correct image URLs
10. ✅ Storage buckets and policies are properly configured

## 🎯 Next Steps

After successful testing:
1. Remove test components (DatabaseTest, AdminTestAuth) from Index.tsx
2. Consider implementing image optimization/CDN
3. Add bulk image management features
4. Implement image gallery/browser for existing uploads
5. Add image metadata (alt text, captions) support
