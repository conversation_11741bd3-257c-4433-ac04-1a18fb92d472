/**
 * Currency formatting utilities for South African Rand (ZAR)
 */

/**
 * Format a number as South African Rand currency
 * @param amount - The amount to format
 * @param options - Optional formatting options
 * @returns Formatted currency string (e.g., "R123.45")
 */
export const formatCurrency = (
  amount: number, 
  options: {
    showSymbol?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string => {
  const {
    showSymbol = true,
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
  } = options;

  const formatted = amount.toLocaleString('en-ZA', {
    minimumFractionDigits,
    maximumFractionDigits,
  });

  return showSymbol ? `R${formatted}` : formatted;
};

/**
 * Format currency using Intl.NumberFormat for more advanced formatting
 * @param amount - The amount to format
 * @param locale - Locale string (defaults to 'en-ZA')
 * @returns Formatted currency string
 */
export const formatCurrencyIntl = (
  amount: number,
  locale: string = 'en-ZA'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Parse a currency string back to a number
 * @param currencyString - String like "R123.45" or "123.45"
 * @returns Parsed number or 0 if invalid
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString) return 0;
  
  // Remove currency symbol and any spaces
  const cleanString = currencyString.replace(/[R\s]/g, '');
  const parsed = parseFloat(cleanString);
  
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Check if an amount qualifies for free shipping
 * @param amount - The amount to check
 * @param threshold - Free shipping threshold (default: R1000)
 * @returns True if qualifies for free shipping
 */
export const qualifiesForFreeShipping = (
  amount: number, 
  threshold: number = 1000
): boolean => {
  return amount >= threshold;
};

/**
 * Calculate shipping cost
 * @param subtotal - Order subtotal
 * @param freeShippingThreshold - Threshold for free shipping (default: R1000)
 * @param standardShippingCost - Standard shipping cost (default: R150)
 * @returns Shipping cost
 */
export const calculateShipping = (
  subtotal: number,
  freeShippingThreshold: number = 1000,
  standardShippingCost: number = 150
): number => {
  return qualifiesForFreeShipping(subtotal, freeShippingThreshold) ? 0 : standardShippingCost;
};

/**
 * Calculate tax (VAT) for South Africa
 * @param amount - Amount to calculate tax on
 * @param taxRate - Tax rate (default: 0.15 for 15% VAT)
 * @returns Tax amount
 */
export const calculateTax = (
  amount: number,
  taxRate: number = 0.15
): number => {
  return amount * taxRate;
};

/**
 * Calculate order totals
 * @param subtotal - Order subtotal
 * @param options - Calculation options
 * @returns Object with all calculated amounts
 */
export const calculateOrderTotals = (
  subtotal: number,
  options: {
    taxRate?: number;
    freeShippingThreshold?: number;
    standardShippingCost?: number;
  } = {}
) => {
  const {
    taxRate = 0.15,
    freeShippingThreshold = 1000,
    standardShippingCost = 150,
  } = options;

  const tax = calculateTax(subtotal, taxRate);
  const shipping = calculateShipping(subtotal, freeShippingThreshold, standardShippingCost);
  const total = subtotal + tax + shipping;

  return {
    subtotal,
    tax,
    shipping,
    total,
    qualifiesForFreeShipping: qualifiesForFreeShipping(subtotal, freeShippingThreshold),
    amountNeededForFreeShipping: Math.max(0, freeShippingThreshold - subtotal),
  };
};
