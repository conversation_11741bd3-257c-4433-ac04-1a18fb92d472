import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types
export interface WishlistItem {
  id: string;
  name: string;
  price: number;
  image_url: string;
  category: string;
  size?: string;
  material?: string;
  added_at: string;
}

export interface WishlistState {
  items: WishlistItem[];
  isLoading: boolean;
}

export type WishlistAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ITEMS'; payload: WishlistItem[] }
  | { type: 'ADD_ITEM'; payload: WishlistItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_WISHLIST' };

export interface WishlistContextType {
  state: WishlistState;
  addItem: (item: Omit<WishlistItem, 'added_at'>) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  clearWishlist: () => Promise<void>;
  isInWishlist: (id: string) => boolean;
  loadWishlist: () => Promise<void>;
}

// Initial state
const initialState: WishlistState = {
  items: [],
  isLoading: false,
};

// Reducer
function wishlistReducer(state: WishlistState, action: WishlistAction): WishlistState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_ITEMS':
      return {
        ...state,
        items: action.payload,
        isLoading: false,
      };

    case 'ADD_ITEM':
      return {
        ...state,
        items: [...state.items, action.payload],
      };

    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload),
      };

    case 'CLEAR_WISHLIST':
      return {
        ...state,
        items: [],
      };

    default:
      return state;
  }
}

// Context
const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

// Provider
interface WishlistProviderProps {
  children: ReactNode;
}

export const WishlistProvider: React.FC<WishlistProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(wishlistReducer, initialState);
  const { user } = useAuth();

  // Load wishlist when user changes
  useEffect(() => {
    if (user) {
      loadWishlist();
    } else {
      // Clear wishlist when user logs out
      dispatch({ type: 'CLEAR_WISHLIST' });
    }
  }, [user]);

  const loadWishlist = async () => {
    if (!user) return;

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Try to load from database first
      const { data, error } = await supabase
        .from('wishlist_items')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        // If table doesn't exist, fall back to localStorage
        console.log('Database table not found, using localStorage fallback');
        const localWishlist = localStorage.getItem(`wishlist_${user.id}`);
        if (localWishlist) {
          const items = JSON.parse(localWishlist);
          dispatch({ type: 'SET_ITEMS', payload: items });
        } else {
          dispatch({ type: 'SET_ITEMS', payload: [] });
        }
        return;
      }

      const wishlistItems: WishlistItem[] = data.map(item => ({
        id: item.product_id,
        name: item.product_name,
        price: item.product_price,
        image_url: item.product_image_url,
        category: item.product_category,
        size: item.product_size,
        material: item.product_material,
        added_at: item.created_at,
      }));

      dispatch({ type: 'SET_ITEMS', payload: wishlistItems });
    } catch (error) {
      console.error('Error loading wishlist:', error);
      // Fall back to localStorage
      const localWishlist = localStorage.getItem(`wishlist_${user.id}`);
      if (localWishlist) {
        const items = JSON.parse(localWishlist);
        dispatch({ type: 'SET_ITEMS', payload: items });
      } else {
        dispatch({ type: 'SET_ITEMS', payload: [] });
      }
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const addItem = async (item: Omit<WishlistItem, 'added_at'>) => {
    if (!user) {
      throw new Error('User must be authenticated to add items to wishlist');
    }

    const wishlistItem: WishlistItem = {
      ...item,
      added_at: new Date().toISOString(),
    };

    try {
      // Try to save to database first
      const { error } = await supabase
        .from('wishlist_items')
        .insert({
          user_id: user.id,
          product_id: item.id,
          product_name: item.name,
          product_price: item.price,
          product_image_url: item.image_url,
          product_category: item.category,
          product_size: item.size,
          product_material: item.material,
        });

      if (error) {
        // Fall back to localStorage
        console.log('Database save failed, using localStorage');
        const currentItems = [...state.items, wishlistItem];
        localStorage.setItem(`wishlist_${user.id}`, JSON.stringify(currentItems));
      }
    } catch (error) {
      // Fall back to localStorage
      console.log('Database error, using localStorage');
      const currentItems = [...state.items, wishlistItem];
      localStorage.setItem(`wishlist_${user.id}`, JSON.stringify(currentItems));
    }

    dispatch({ type: 'ADD_ITEM', payload: wishlistItem });
    toast.success(`${item.name} added to wishlist!`);
  };

  const removeItem = async (id: string) => {
    if (!user) return;

    const item = state.items.find(item => item.id === id);

    try {
      // Try to remove from database
      const { error } = await supabase
        .from('wishlist_items')
        .delete()
        .eq('user_id', user.id)
        .eq('product_id', id);

      if (error) {
        // Fall back to localStorage
        console.log('Database delete failed, using localStorage');
        const updatedItems = state.items.filter(item => item.id !== id);
        localStorage.setItem(`wishlist_${user.id}`, JSON.stringify(updatedItems));
      }
    } catch (error) {
      // Fall back to localStorage
      console.log('Database error, using localStorage');
      const updatedItems = state.items.filter(item => item.id !== id);
      localStorage.setItem(`wishlist_${user.id}`, JSON.stringify(updatedItems));
    }

    dispatch({ type: 'REMOVE_ITEM', payload: id });

    if (item) {
      toast.success(`${item.name} removed from wishlist`);
    }
  };

  const clearWishlist = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('wishlist_items')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error clearing wishlist:', error);
        toast.error('Failed to clear wishlist');
        return;
      }

      dispatch({ type: 'CLEAR_WISHLIST' });
      toast.success('Wishlist cleared');
    } catch (error) {
      console.error('Error clearing wishlist:', error);
      toast.error('Failed to clear wishlist');
    }
  };

  const isInWishlist = (id: string): boolean => {
    return state.items.some(item => item.id === id);
  };

  const value: WishlistContextType = {
    state,
    addItem,
    removeItem,
    clearWishlist,
    isInWishlist,
    loadWishlist,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};

// Hook
export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
