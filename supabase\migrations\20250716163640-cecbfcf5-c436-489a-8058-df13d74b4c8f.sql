-- Create artist profile table
CREATE TABLE public.artist_profile (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  bio TEXT,
  full_biography TEXT,
  portrait_url TEXT,
  hero_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create portfolio gallery table
CREATE TABLE public.portfolio_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  category TEXT,
  featured B<PERSON>OLEA<PERSON> DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create products table
CREATE TABLE public.products (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  image_url TEXT NOT NULL,
  category TEXT,
  material TEXT,
  size TEXT,
  featured BOOLEAN DEFAULT false,
  in_stock BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create contact form submissions table
CREATE TABLE public.contact_submissions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  subject TEXT,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.artist_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_submissions ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access to artist, portfolio, and products
CREATE POLICY "Artist profile is publicly readable" 
ON public.artist_profile 
FOR SELECT 
USING (true);

CREATE POLICY "Portfolio items are publicly readable" 
ON public.portfolio_items 
FOR SELECT 
USING (true);

CREATE POLICY "Products are publicly readable" 
ON public.products 
FOR SELECT 
USING (true);

-- Contact submissions - allow anyone to insert, but only authenticated users to read
CREATE POLICY "Anyone can submit contact forms" 
ON public.contact_submissions 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Only authenticated users can view contact submissions" 
ON public.contact_submissions 
FOR SELECT 
USING (auth.role() = 'authenticated');

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_artist_profile_updated_at
BEFORE UPDATE ON public.artist_profile
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_portfolio_items_updated_at
BEFORE UPDATE ON public.portfolio_items
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_products_updated_at
BEFORE UPDATE ON public.products
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample data
INSERT INTO public.artist_profile (name, bio, full_biography, portrait_url, hero_image_url) VALUES 
('Marina Delmar', 'Aquascaping artist bringing underwater dreams to life through custom decor.', 
'Marina Delmar has been creating custom aquarium decor for over 8 years, transforming ordinary fish tanks into extraordinary underwater worlds. Her passion for marine life and artistic vision combine to create pieces that not only beautify aquariums but also provide enriching environments for aquatic inhabitants. Each piece is carefully crafted with non-toxic materials and designed to enhance both the aesthetic and ecological value of your aquarium.',
'https://images.unsplash.com/photo-1494790108755-2616b612b5bc?w=400&h=400&fit=crop&crop=face',
'https://images.unsplash.com/photo-1500375592092-40eb2168fd21?w=1200&h=800&fit=crop');

INSERT INTO public.portfolio_items (title, description, image_url, category, featured, sort_order) VALUES 
('Coral Garden Paradise', 'A vibrant coral reef replica with caves and swimming channels', 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=600&h=400&fit=crop', 'Coral Reefs', true, 1),
('Ancient Ruins Theme', 'Mysterious underwater ruins with detailed stonework', 'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=600&h=400&fit=crop', 'Themed Decor', true, 2),
('Natural Rock Formation', 'Realistic rock caves and overhangs for natural habitat', 'https://images.unsplash.com/photo-1583415223065-5d4e7c4d1e5d?w=600&h=400&fit=crop', 'Natural', true, 3),
('Sunken Ship Adventure', 'Detailed pirate ship wreck with treasure chest details', 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=600&h=400&fit=crop', 'Themed Decor', false, 4);

INSERT INTO public.products (name, description, price, image_url, category, material, size, featured, sort_order) VALUES 
('Coral Castle Set', 'Hand-crafted coral replica set with multiple pieces', 89.99, 'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=400&h=300&fit=crop', 'Coral Decor', 'Resin', 'Medium', true, 1),
('Cave System Deluxe', 'Multi-level cave system with swim-through tunnels', 129.99, 'https://images.unsplash.com/photo-1583415223065-5d4e7c4d1e5d?w=400&h=300&fit=crop', 'Caves', 'Ceramic', 'Large', true, 2),
('Treasure Chest with Coins', 'Opening treasure chest with scattered gold coins', 34.99, 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop', 'Accessories', 'Resin', 'Small', true, 3),
('Driftwood Arch', 'Natural-looking driftwood arch perfect for planted tanks', 45.99, 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop', 'Natural', 'Treated Wood', 'Medium', false, 4),
('Miniature Lighthouse', 'Detailed lighthouse with working LED light', 67.99, 'https://images.unsplash.com/photo-1472396961693-142e6e269027?w=400&h=300&fit=crop', 'Themed Decor', 'Resin', 'Medium', true, 5),
('Bubble Wall Feature', 'Decorative bubble wall with integrated air system', 156.99, 'https://images.unsplash.com/photo-1618160702438-9b02040d0a901?w=400&h=300&fit=crop', 'Features', 'Acrylic', 'Large', true, 6);