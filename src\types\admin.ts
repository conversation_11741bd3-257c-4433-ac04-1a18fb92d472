// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface SortableEntity {
  sort_order: number;
}

export interface ActivatableEntity {
  is_active: boolean;
}

export interface SEOEntity {
  meta_title?: string;
  meta_description?: string;
}

// Product types
export interface Product extends BaseEntity, SortableEntity, SEOEntity {
  name: string;
  description?: string;
  price: number;
  category: string;
  image_url?: string;
  material?: string;
  size?: string;
  in_stock: boolean;
  featured: boolean;
  stock_quantity: number;
  sku?: string;
  tags?: string[];
  weight?: number;
  dimensions?: string;
}

export interface ProductCategory extends BaseEntity, SortableEntity, ActivatableEntity {
  name: string;
  description?: string;
  slug: string;
  parent_id?: string;
  parent?: ProductCategory;
  children?: ProductCategory[];
}

export interface ProductVariant extends BaseEntity, ActivatableEntity {
  product_id: string;
  name: string;
  sku?: string;
  price?: number;
  stock_quantity: number;
  variant_options: Record<string, any>;
}

export interface ProductImage extends BaseEntity, SortableEntity {
  product_id: string;
  image_url: string;
  alt_text?: string;
  is_primary: boolean;
}

// Order types
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

export interface Order extends BaseEntity {
  order_number: string;
  user_id: string;
  total_amount: number;
  status: OrderStatus;
  payment_status: PaymentStatus;
  shipping_method?: string;
  tracking_number?: string;
  notes?: string;
  internal_notes?: string;
  user_profiles?: UserProfile;
  order_items?: OrderItem[];
  order_addresses?: OrderAddress[];
}

export interface OrderItem extends BaseEntity {
  order_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product?: Product;
}

export interface OrderAddress extends BaseEntity {
  order_id: string;
  type: 'billing' | 'shipping';
  first_name?: string;
  last_name?: string;
  company?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  phone?: string;
}

export interface OrderStatusHistory extends BaseEntity {
  order_id: string;
  status: OrderStatus;
  notes?: string;
  created_by?: string;
}

// User types
export type UserRole = 'customer' | 'admin' | 'super_admin';

export interface UserProfile extends BaseEntity {
  first_name?: string;
  last_name?: string;
  email: string;
  phone?: string;
  role: UserRole;
  orders?: Order[];
}

// Content types
export type BlogPostStatus = 'draft' | 'published' | 'archived';

export interface BlogPost extends BaseEntity, SEOEntity {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image_url?: string;
  author_id?: string;
  category_id?: string;
  status: BlogPostStatus;
  featured: boolean;
  published_at?: string;
  category?: BlogCategory;
}

export interface BlogCategory extends BaseEntity, SortableEntity, ActivatableEntity {
  name: string;
  slug: string;
  description?: string;
  color: string;
}

export interface FAQ extends BaseEntity, SortableEntity, ActivatableEntity {
  question: string;
  answer: string;
  category_id?: string;
  helpful_count: number;
  not_helpful_count: number;
  category?: FAQCategory;
}

export interface FAQCategory extends BaseEntity, SortableEntity, ActivatableEntity {
  name: string;
  slug: string;
  description?: string;
}

// Contact types
export type ContactStatus = 'new' | 'in_progress' | 'resolved' | 'closed';
export type ContactPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface ContactSubmission extends BaseEntity {
  name: string;
  email: string;
  subject?: string;
  message: string;
  phone?: string;
  status: ContactStatus;
  priority: ContactPriority;
  assigned_to?: string;
  responded_at?: string;
}

// Newsletter types
export type SubscriberStatus = 'active' | 'unsubscribed' | 'bounced';

export interface NewsletterSubscriber extends BaseEntity {
  email: string;
  name?: string;
  status: SubscriberStatus;
  source?: string;
  tags?: string[];
  subscribed_at: string;
  unsubscribed_at?: string;
}

// Portfolio types
export interface PortfolioItem extends BaseEntity, SortableEntity {
  title: string;
  description?: string;
  image_url: string;
  category: string;
  featured: boolean;
}

// Artist types
export interface ArtistProfile extends BaseEntity {
  name: string;
  bio?: string;
  full_biography?: string;
  portrait_url?: string;
  hero_image_url?: string;
  website_url?: string;
  social_instagram?: string;
  social_facebook?: string;
  years_experience?: number;
  total_pieces_created?: number;
}

// Site settings types
export interface SiteSetting extends BaseEntity {
  key: string;
  value: any;
  description?: string;
}

// Admin activity types
export interface AdminActivityLog extends BaseEntity {
  user_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

// Dashboard types
export interface DashboardStats {
  totalUsers: number;
  totalOrders: number;
  totalProducts: number;
  totalRevenue: number;
  recentOrders: Order[];
  popularProducts: Product[];
  salesTrends: {
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  customerInsights: {
    newCustomers: number;
    returningCustomers: number;
    averageOrderValue: number;
  };
  inventoryAlerts: {
    lowStock: Product[];
    outOfStock: Product[];
  };
  monthlyRevenue: { month: string; revenue: number }[];
}

// Form types
export interface FormState<T> {
  data: T;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  category?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type CreateInput<T extends BaseEntity> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type UpdateInput<T extends BaseEntity> = Partial<CreateInput<T>>;

// Component prop types
export interface AdminComponentProps {
  className?: string;
  loading?: boolean;
  error?: string | null;
}

export interface TableProps<T> extends AdminComponentProps {
  data: T[];
  columns: TableColumn<T>[];
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: FilterParams) => void;
  pagination?: PaginationParams;
  onPageChange?: (page: number) => void;
}

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface FormProps<T> extends AdminComponentProps {
  initialData?: Partial<T>;
  onSubmit: (data: T) => Promise<void>;
  onCancel?: () => void;
  validationSchema?: any;
}

// Event types
export interface AdminEvent {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

// Search types
export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  filters: FilterParams;
}

// File upload types
export interface FileUploadResult {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// Notification types
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  timestamp: string;
  read: boolean;
}
