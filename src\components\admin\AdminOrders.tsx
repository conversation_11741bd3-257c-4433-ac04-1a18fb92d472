import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { Eye, Package, Truck, CheckCircle, Edit, Mail, FileText, Clock, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Order {
  id: string;
  order_number: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  payment_status: 'pending' | 'refunded' | 'completed' | 'failed';
  total_amount: number;
  created_at: string;
  user_profiles?: {
    first_name: string;
    last_name: string;
    email: string;
  } | null;
  order_items?: {
    id: string;
    product_name: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  }[];
  order_addresses?: {
    type: string;
    first_name: string;
    last_name: string;
    address_line_1: string;
    address_line_2: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  }[];
}

export const AdminOrders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [statusUpdate, setStatusUpdate] = useState({
    status: '',
    tracking_number: '',
    shipping_method: '',
    notes: ''
  });

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          user_profiles(first_name, last_name, email),
          order_items(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setOrders((data || []) as any);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (orderId: string, newStatus: string, updates: any = {}) => {
    try {
      // Update order status
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: newStatus,
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (orderError) throw orderError;

      // Add to status history
      const { error: historyError } = await supabase
        .from('order_status_history')
        .insert({
          order_id: orderId,
          status: newStatus,
          notes: updates.notes || `Status updated to ${newStatus}`,
          created_at: new Date().toISOString()
        });

      if (historyError) console.warn('Failed to add status history:', historyError);

      toast.success('Order status updated successfully!');
      fetchOrders();
      setShowStatusDialog(false);
    } catch (error: any) {
      console.error('Error updating order status:', error);
      toast.error(`Failed to update order status: ${error.message}`);
    }
  };

  const handleQuickStatusUpdate = (order: Order, newStatus: string) => {
    handleStatusUpdate(order.id, newStatus);
  };

  const handleDetailedStatusUpdate = (order: Order) => {
    setSelectedOrder(order);
    setStatusUpdate({
      status: order.status,
      tracking_number: order.tracking_number || '',
      shipping_method: order.shipping_method || '',
      notes: ''
    });
    setShowStatusDialog(true);
  };

  const submitStatusUpdate = () => {
    if (!selectedOrder) return;

    const updates: any = {
      notes: statusUpdate.notes
    };

    if (statusUpdate.tracking_number) {
      updates.tracking_number = statusUpdate.tracking_number;
    }

    if (statusUpdate.shipping_method) {
      updates.shipping_method = statusUpdate.shipping_method;
    }

    handleStatusUpdate(selectedOrder.id, statusUpdate.status, updates);
  };

  const updateOrderStatus = async (orderId: string, newStatus: Order['status']) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('id', orderId);

      if (error) throw error;

      // Add to status history
      await supabase
        .from('order_status_history')
        .insert({
          order_id: orderId,
          status: newStatus,
          notes: `Status updated to ${newStatus}`
        });

      toast.success('Order status updated successfully!');
      fetchOrders();
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Failed to update order status');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-orange-100 text-orange-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const OrderDetails = ({ order }: { order: Order }) => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold mb-2">Order Information</h4>
          <div className="space-y-2 text-sm">
            <p><strong>Order #:</strong> {order.order_number}</p>
            <p><strong>Date:</strong> {new Date(order.created_at).toLocaleString()}</p>
            <p><strong>Total:</strong> ${Number(order.total_amount).toFixed(2)}</p>
            <div className="flex items-center gap-2">
              <strong>Status:</strong>
              <Select
                value={order.status}
                onValueChange={(value) => updateOrderStatus(order.id, value as Order['status'])}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-2">Customer Information</h4>
          <div className="space-y-2 text-sm">
            <p><strong>Name:</strong> {order.user_profiles?.first_name} {order.user_profiles?.last_name}</p>
            <p><strong>Email:</strong> {order.user_profiles?.email}</p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-semibold mb-2">Order Items</h4>
        <div className="space-y-2">
          {order.order_items?.map((item) => (
            <div key={item.id} className="flex justify-between items-center p-3 border rounded">
              <div>
                <p className="font-medium">{item.product_name}</p>
                <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
              </div>
              <div className="text-right">
                <p className="font-medium">${Number(item.total_price).toFixed(2)}</p>
                <p className="text-sm text-gray-600">${Number(item.unit_price).toFixed(2)} each</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {order.order_addresses && order.order_addresses.length > 0 && (
        <div>
          <h4 className="font-semibold mb-2">Shipping Address</h4>
          {order.order_addresses.map((address, index) => (
            <div key={index} className="p-3 border rounded">
              <p className="font-medium">{address.first_name} {address.last_name}</p>
              <p>{address.address_line_1}</p>
              {address.address_line_2 && <p>{address.address_line_2}</p>}
              <p>{address.city}, {address.state} {address.postal_code}</p>
              <p>{address.country}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  if (loading) {
    return <div className="text-center py-8">Loading orders...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
        <p className="text-muted-foreground">
          Manage customer orders and track their status
        </p>
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-4">
                    <h3 className="font-semibold">Order #{order.order_number}</h3>
                    <Badge className={getStatusColor(order.status)}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </Badge>
                    <Badge className={getPaymentStatusColor(order.payment_status)}>
                      {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    {order.user_profiles?.first_name} {order.user_profiles?.last_name} • 
                    {new Date(order.created_at).toLocaleDateString()}
                  </p>
                  <p className="font-medium text-blue-600">
                    ${Number(order.total_amount).toFixed(2)}
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  {/* Quick Status Update Buttons */}
                  {order.status === 'pending' && (
                    <Button
                      size="sm"
                      onClick={() => handleQuickStatusUpdate(order, 'confirmed')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Confirm
                    </Button>
                  )}
                  {order.status === 'confirmed' && (
                    <Button
                      size="sm"
                      onClick={() => handleQuickStatusUpdate(order, 'processing')}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Package className="h-4 w-4 mr-1" />
                      Process
                    </Button>
                  )}
                  {order.status === 'processing' && (
                    <Button
                      size="sm"
                      onClick={() => handleDetailedStatusUpdate(order)}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Truck className="h-4 w-4 mr-1" />
                      Ship
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDetailedStatusUpdate(order)}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Update
                  </Button>

                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" onClick={() => setSelectedOrder(order)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Order Details - #{order.order_number}</DialogTitle>
                        <DialogDescription>
                          Complete order information and management
                        </DialogDescription>
                      </DialogHeader>
                      <OrderDetails order={order} />
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {orders.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">No orders yet</h3>
            <p className="text-gray-600">Orders will appear here once customers start purchasing.</p>
          </CardContent>
        </Card>
      )}

      {/* Status Update Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
            <DialogDescription>
              Update the status and add tracking information for order #{selectedOrder?.order_number}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Order Status</Label>
              <Select
                value={statusUpdate.status}
                onValueChange={(value) => setStatusUpdate({ ...statusUpdate, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tracking_number">Tracking Number</Label>
              <Input
                id="tracking_number"
                value={statusUpdate.tracking_number}
                onChange={(e) => setStatusUpdate({ ...statusUpdate, tracking_number: e.target.value })}
                placeholder="Enter tracking number"
              />
            </div>

            <div>
              <Label htmlFor="shipping_method">Shipping Method</Label>
              <Select
                value={statusUpdate.shipping_method}
                onValueChange={(value) => setStatusUpdate({ ...statusUpdate, shipping_method: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select shipping method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard Shipping</SelectItem>
                  <SelectItem value="express">Express Shipping</SelectItem>
                  <SelectItem value="overnight">Overnight Shipping</SelectItem>
                  <SelectItem value="pickup">Local Pickup</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={statusUpdate.notes}
                onChange={(e) => setStatusUpdate({ ...statusUpdate, notes: e.target.value })}
                placeholder="Add notes about this status update..."
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
                Cancel
              </Button>
              <Button onClick={submitStatusUpdate}>
                Update Status
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};