-- Quick fix for site_settings data format issue
-- Run this in Supabase SQL editor to fix the immediate problem

-- First, let's see what data we have
SELECT key, value, pg_typeof(value) as value_type FROM site_settings;

-- Fix the data by updating unquoted strings to be properly quoted JSON
UPDATE site_settings 
SET value = '"' || value || '"'
WHERE value IS NOT NULL 
  AND value != 'true' 
  AND value != 'false'
  AND value !~ '^[0-9]+\.?[0-9]*$'  -- not a number
  AND value !~ '^".*"$'             -- not already quoted
  AND value !~ '^\{.*\}$'           -- not an object
  AND value !~ '^\[.*\]$';          -- not an array

-- Verify the fix
SELECT key, value, pg_typeof(value) as value_type FROM site_settings;

-- Test that all values can be converted to JSONB
SELECT key, value::JSONB as jsonb_value FROM site_settings;
