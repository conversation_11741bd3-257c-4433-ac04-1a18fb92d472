# Admin Save Issues - Fixes & Testing Guide

## 🔧 Issues Fixed

### 1. **Database Schema Mismatches**
- **Products**: Added validation for required `category` field (NOT NULL in DB)
- **Portfolio**: Added validation for required `category` and `image_url` fields (NOT NULL in DB)
- **Site Settings**: Fixed data type handling for `value` column (text vs JSONB)

### 2. **Form Validation Issues**
- Added client-side validation for all required fields
- Added proper error messages with field-specific feedback
- Added visual indicators (*) for required fields

### 3. **Database Permissions (RLS Policies)**
- Created comprehensive admin policies for all tables
- Added public read policies for content that appears on public pages
- Fixed circular dependency issues in user_profiles policies

### 4. **Error Handling Improvements**
- Enhanced error messages to show specific database errors
- Added proper validation before database operations
- Improved user feedback with detailed error descriptions

## 🚀 Setup Instructions

### 1. Apply Database Migration
```bash
# Run the fix migration
supabase db push

# Or manually execute in Supabase SQL editor:
# File: supabase/migrations/20250719080000-fix-admin-save-issues.sql
```

### 2. Verify Migration Applied
In Supabase SQL editor, run:
```sql
-- Check admin permissions for current user
SELECT * FROM check_admin_permissions();

-- Verify site_settings value column type
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'site_settings' AND column_name = 'value';
```

## 🧪 Testing Guide

### Prerequisites
1. Admin user logged in
2. Migration applied successfully
3. Storage buckets configured (from previous setup)

### Test 1: Products Save
**Steps:**
1. Go to Admin → Products → Add Product
2. Fill in form:
   - **Name**: "Test Product" ✅
   - **Description**: "Test description"
   - **Price**: 29.99 ✅
   - **Category**: "Decorations" ✅ (REQUIRED)
   - **Material**: "Ceramic"
   - **Size**: "Medium"
   - Upload an image ✅
3. Click "Create Product"

**Expected Result**: ✅ Success message, product appears in list

**Test Error Cases:**
- Leave Name empty → "Product name is required"
- Set Price to 0 → "Product price must be greater than 0"  
- Leave Category empty → "Product category is required"

### Test 2: Portfolio Save
**Steps:**
1. Go to Admin → Portfolio → Add Portfolio Item
2. Fill in form:
   - **Title**: "Test Artwork" ✅
   - **Description**: "Test artwork description"
   - **Category**: "Sculpture" ✅ (REQUIRED)
   - **Sort Order**: 1
   - Upload an image ✅
3. Click "Create Portfolio Item"

**Expected Result**: ✅ Success message, item appears in list

**Test Error Cases:**
- Leave Title empty → "Portfolio title is required"
- Leave Category empty → "Portfolio category is required"
- Don't upload image → "Portfolio image is required"

### Test 3: Homepage Settings Save
**Steps:**
1. Go to Admin → Homepage
2. Fill in form:
   - **Hero Title**: "Test Title" ✅ (REQUIRED)
   - **Hero Subtitle**: "Test subtitle"
   - Upload hero image
   - **About Title**: "About Test"
   - **About Description**: "Test description"
   - Upload about image
3. Click "Save Changes"

**Expected Result**: ✅ Success message, settings saved

**Test Error Cases:**
- Leave Hero Title empty → "Hero title is required"

### Test 4: Artist Profile Save
**Steps:**
1. Go to Admin → Artist
2. Fill in form:
   - **Artist Name**: "Test Artist" ✅ (REQUIRED)
   - **Short Bio**: "Test bio"
   - **Full Biography**: "Detailed test biography"
   - Upload portrait image
   - Upload hero image
   - **Website URL**: "https://example.com"
   - **Instagram**: "@testartist"
   - **Years Experience**: 5
   - **Total Pieces**: 100
3. Click "Save Profile"

**Expected Result**: ✅ Success message, profile saved

**Test Error Cases:**
- Leave Artist Name empty → "Artist name is required"

## 🔍 Debugging Tools

### Check Admin Permissions
Run in Supabase SQL editor:
```sql
-- Check your admin permissions
SELECT * FROM check_admin_permissions();

-- Should show:
-- table_name | can_select | can_insert | can_update | can_delete | user_role
-- products   | true       | true       | true       | true       | admin/super_admin
-- etc...
```

### Check RLS Policies
```sql
-- View all policies for admin tables
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('products', 'portfolio_items', 'artist_profile', 'site_settings');
```

### Test Database Operations Directly
```sql
-- Test product insert
INSERT INTO products (name, price, category, image_url) 
VALUES ('Test Product', 29.99, 'Test Category', 'https://example.com/image.jpg');

-- Test portfolio insert  
INSERT INTO portfolio_items (title, image_url, category) 
VALUES ('Test Portfolio', 'https://example.com/image.jpg', 'Test Category');

-- Test artist profile insert
INSERT INTO artist_profile (name) 
VALUES ('Test Artist');

-- Test site settings upsert
INSERT INTO site_settings (key, value, description) 
VALUES ('test_key', '"test_value"', 'Test setting')
ON CONFLICT (key) DO UPDATE SET value = EXCLUDED.value;
```

## ❌ Common Error Solutions

### "Failed to save product/portfolio/etc"
1. **Check browser console** for specific error message
2. **Verify required fields** are filled (marked with *)
3. **Check admin role** - run `SELECT role FROM user_profiles WHERE id = auth.uid()`
4. **Apply migration** if not done already

### "Permission denied" errors
1. **Check RLS policies** - run the debug queries above
2. **Verify admin role** in user_profiles table
3. **Re-apply migration** to fix policies

### "Column does not exist" errors
1. **Check database schema** matches form fields
2. **Apply migration** to fix schema issues
3. **Regenerate types** if using TypeScript

### Image uploads work but save fails
1. **Check required fields** - category is now required for products/portfolio
2. **Verify form validation** - all required fields must be filled
3. **Check error message** in browser console for specific issue

## ✅ Success Criteria

After applying fixes, you should be able to:

1. ✅ **Create Products** with name, price, category, and image
2. ✅ **Create Portfolio Items** with title, category, and image  
3. ✅ **Save Homepage Settings** with hero title and images
4. ✅ **Save Artist Profile** with name and images
5. ✅ **See detailed error messages** for validation failures
6. ✅ **View saved content** on public pages
7. ✅ **Edit existing items** successfully

## 🎯 Next Steps

After successful testing:
1. **Remove test data** created during testing
2. **Add real content** using the admin interface
3. **Verify public pages** display the new content correctly
4. **Consider additional validation** rules as needed
5. **Monitor error logs** for any remaining issues

The admin save functionality should now work reliably! 🎉
