-- Create storage buckets for image uploads
-- This migration sets up the complete storage infrastructure for the admin dashboard

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('product-images', 'product-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('portfolio-images', 'portfolio-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('artist-images', 'artist-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('homepage-images', 'homepage-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for storage buckets
-- Allow public read access to all image buckets
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT USING (bucket_id = 'product-images');

CREATE POLICY "Public read access for portfolio images" ON storage.objects
  FOR SELECT USING (bucket_id = 'portfolio-images');

CREATE POLICY "Public read access for artist images" ON storage.objects
  FOR SELECT USING (bucket_id = 'artist-images');

CREATE POLICY "Public read access for homepage images" ON storage.objects
  FOR SELECT USING (bucket_id = 'homepage-images');

-- Allow authenticated users to upload images (admins will be authenticated)
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'product-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can upload portfolio images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'portfolio-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can upload artist images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'artist-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can upload homepage images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'homepage-images' 
    AND auth.role() = 'authenticated'
  );

-- Allow authenticated users to update/delete images they uploaded
CREATE POLICY "Authenticated users can update product images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'product-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can delete product images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'product-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can update portfolio images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'portfolio-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can delete portfolio images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'portfolio-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can update artist images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'artist-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can delete artist images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'artist-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can update homepage images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'homepage-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Authenticated users can delete homepage images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'homepage-images' 
    AND auth.role() = 'authenticated'
  );

-- Create helper function to generate unique file names
CREATE OR REPLACE FUNCTION generate_unique_filename(original_name TEXT, bucket_name TEXT)
RETURNS TEXT AS $$
DECLARE
  file_extension TEXT;
  base_name TEXT;
  unique_name TEXT;
  counter INTEGER := 0;
BEGIN
  -- Extract file extension
  file_extension := LOWER(SUBSTRING(original_name FROM '\.([^.]*)$'));
  base_name := SUBSTRING(original_name FROM '^(.*)\.([^.]*)$');
  
  -- If no extension found, use the whole name
  IF file_extension IS NULL THEN
    file_extension := '';
    base_name := original_name;
  ELSE
    file_extension := '.' || file_extension;
  END IF;
  
  -- Generate unique name with timestamp and random component
  unique_name := LOWER(REPLACE(base_name, ' ', '-')) || '-' || 
                 EXTRACT(EPOCH FROM NOW())::BIGINT || '-' || 
                 FLOOR(RANDOM() * 10000)::TEXT || 
                 file_extension;
  
  RETURN unique_name;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old unused images (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_unused_images()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- This function can be called periodically to clean up unused images
  -- For now, it's just a placeholder - actual cleanup logic would need
  -- to check which images are still referenced in the database
  
  -- Example cleanup logic (commented out for safety):
  -- DELETE FROM storage.objects 
  -- WHERE bucket_id IN ('product-images', 'portfolio-images', 'artist-images', 'homepage-images')
  -- AND created_at < NOW() - INTERVAL '30 days'
  -- AND name NOT IN (
  --   SELECT DISTINCT image_url FROM products WHERE image_url IS NOT NULL
  --   UNION
  --   SELECT DISTINCT image_url FROM portfolio_items WHERE image_url IS NOT NULL
  --   -- Add other tables as needed
  -- );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
