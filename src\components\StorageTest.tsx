import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
}

export const StorageTest = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Storage Connection', status: 'pending', message: 'Not tested' },
    { name: 'Bucket Access', status: 'pending', message: 'Not tested' },
    { name: 'Upload Permissions', status: 'pending', message: 'Not tested' },
  ]);
  const [testing, setTesting] = useState(false);

  const updateTest = (index: number, status: 'success' | 'error', message: string) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message } : test
    ));
  };

  const runTests = async () => {
    setTesting(true);
    
    // Test 1: Storage Connection
    try {
      const { data, error } = await supabase.storage.listBuckets();
      if (error) throw error;
      updateTest(0, 'success', `Found ${data.length} buckets`);
    } catch (error: any) {
      updateTest(0, 'error', error.message);
    }

    // Test 2: Bucket Access
    try {
      const buckets = ['product-images', 'portfolio-images', 'artist-images', 'homepage-images'];
      const results = await Promise.all(
        buckets.map(bucket => supabase.storage.from(bucket).list())
      );
      
      const errors = results.filter(r => r.error);
      if (errors.length > 0) {
        updateTest(1, 'error', `${errors.length} buckets inaccessible`);
      } else {
        updateTest(1, 'success', 'All 4 buckets accessible');
      }
    } catch (error: any) {
      updateTest(1, 'error', error.message);
    }

    // Test 3: Upload Permissions (simulate)
    try {
      // Create a small test blob
      const testBlob = new Blob(['test'], { type: 'text/plain' });
      const testFile = new File([testBlob], 'test.txt', { type: 'text/plain' });
      
      // Try to upload to product-images bucket
      const { error } = await supabase.storage
        .from('product-images')
        .upload(`test-${Date.now()}.txt`, testFile);
      
      if (error) {
        if (error.message.includes('mime type')) {
          updateTest(2, 'success', 'Upload permissions work (MIME type validation active)');
        } else {
          updateTest(2, 'error', error.message);
        }
      } else {
        updateTest(2, 'success', 'Upload permissions work');
      }
    } catch (error: any) {
      updateTest(2, 'error', error.message);
    }

    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <Card className="fixed bottom-4 left-4 w-80 z-50">
      <CardHeader>
        <CardTitle className="text-sm">Storage System Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runTests} 
          disabled={testing}
          size="sm"
          className="w-full"
        >
          {testing ? 'Testing...' : 'Run Storage Tests'}
        </Button>
        
        <div className="space-y-2">
          {tests.map((test, index) => (
            <div key={index} className="flex items-center gap-2 text-xs">
              {getStatusIcon(test.status)}
              <div className="flex-1">
                <div className="font-medium">{test.name}</div>
                <div className="text-muted-foreground">{test.message}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
