import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Please enter a valid email address');
export const phoneSchema = z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number').optional();
export const urlSchema = z.string().url('Please enter a valid URL').optional();
export const slugSchema = z.string().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must contain only lowercase letters, numbers, and hyphens');

// Product validation
export const productSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(255, 'Product name must be less than 255 characters'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  category: z.string().min(1, 'Category is required'),
  image_url: urlSchema,
  material: z.string().optional(),
  size: z.string().optional(),
  in_stock: z.boolean().default(true),
  featured: z.boolean().default(false),
  stock_quantity: z.number().min(0, 'Stock quantity must be non-negative').default(0),
  sku: z.string().optional(),
  meta_title: z.string().max(60, 'Meta title should be less than 60 characters').optional(),
  meta_description: z.string().max(160, 'Meta description should be less than 160 characters').optional(),
  tags: z.array(z.string()).optional(),
  weight: z.number().min(0, 'Weight must be positive').optional(),
  dimensions: z.string().optional(),
});

// Category validation
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name must be less than 100 characters'),
  description: z.string().optional(),
  slug: slugSchema,
  parent_id: z.string().uuid().optional(),
  sort_order: z.number().min(0, 'Sort order must be non-negative').default(0),
  is_active: z.boolean().default(true),
});

// Blog post validation
export const blogPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title must be less than 255 characters'),
  slug: slugSchema,
  content: z.string().min(1, 'Content is required'),
  excerpt: z.string().max(500, 'Excerpt must be less than 500 characters').optional(),
  featured_image_url: urlSchema,
  category_id: z.string().uuid().optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  featured: z.boolean().default(false),
  meta_title: z.string().max(60, 'Meta title should be less than 60 characters').optional(),
  meta_description: z.string().max(160, 'Meta description should be less than 160 characters').optional(),
});

// FAQ validation
export const faqSchema = z.object({
  question: z.string().min(1, 'Question is required').max(500, 'Question must be less than 500 characters'),
  answer: z.string().min(1, 'Answer is required'),
  category_id: z.string().uuid().optional(),
  sort_order: z.number().min(0, 'Sort order must be non-negative').default(0),
  is_active: z.boolean().default(true),
});

// Contact submission validation
export const contactSubmissionSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: emailSchema,
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject must be less than 200 characters'),
  message: z.string().min(1, 'Message is required').max(5000, 'Message must be less than 5000 characters'),
  phone: phoneSchema,
});

// Newsletter subscriber validation
export const newsletterSubscriberSchema = z.object({
  email: emailSchema,
  name: z.string().max(100, 'Name must be less than 100 characters').optional(),
  tags: z.array(z.string()).optional(),
});

// User profile validation
export const userProfileSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  email: emailSchema,
  phone: phoneSchema,
  role: z.enum(['customer', 'admin', 'super_admin']).default('customer'),
});

// Order validation
export const orderSchema = z.object({
  user_id: z.string().uuid(),
  total_amount: z.number().min(0, 'Total amount must be positive'),
  status: z.enum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']).default('pending'),
  payment_status: z.enum(['pending', 'completed', 'failed', 'refunded']).default('pending'),
  shipping_method: z.string().optional(),
  tracking_number: z.string().optional(),
  notes: z.string().optional(),
  internal_notes: z.string().optional(),
});

// Artist profile validation
export const artistProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  bio: z.string().optional(),
  full_biography: z.string().optional(),
  portrait_url: urlSchema,
  hero_image_url: urlSchema,
  website_url: urlSchema,
  social_instagram: z.string().optional(),
  social_facebook: z.string().optional(),
  years_experience: z.number().min(0, 'Years of experience must be non-negative').optional(),
  total_pieces_created: z.number().min(0, 'Total pieces created must be non-negative').optional(),
});

// Portfolio item validation
export const portfolioItemSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title must be less than 255 characters'),
  description: z.string().optional(),
  image_url: z.string().min(1, 'Image is required'),
  category: z.string().min(1, 'Category is required'),
  featured: z.boolean().default(false),
  sort_order: z.number().min(0, 'Sort order must be non-negative').default(0),
});

// Site settings validation
export const siteSettingsSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  value: z.any(),
  description: z.string().optional(),
});

// Validation helper functions
export const validateData = <T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } => {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      success: false,
      errors: ['Validation failed']
    };
  }
};

export const getFieldError = (errors: z.ZodError, fieldName: string): string | undefined => {
  const fieldError = errors.errors.find(error => error.path.includes(fieldName));
  return fieldError?.message;
};

// Form validation hook
export const useFormValidation = <T>(schema: z.ZodSchema<T>) => {
  const validate = (data: unknown) => validateData(schema, data);
  
  const validateField = (fieldName: string, value: unknown) => {
    try {
      const fieldSchema = schema.shape[fieldName as keyof typeof schema.shape];
      if (fieldSchema) {
        fieldSchema.parse(value);
        return null;
      }
      return null;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Invalid value';
      }
      return 'Invalid value';
    }
  };

  return { validate, validateField };
};
