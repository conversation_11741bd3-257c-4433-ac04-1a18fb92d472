import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to external service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }

    // Show toast notification
    toast.error('Something went wrong. Please try refreshing the page.');
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-center text-muted-foreground">
                We're sorry, but something unexpected happened. Please try one of the options below.
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4 p-3 bg-muted rounded-lg">
                  <summary className="cursor-pointer text-sm font-medium">
                    Error Details (Development)
                  </summary>
                  <div className="mt-2 text-xs font-mono">
                    <p className="text-red-600 font-semibold">{this.state.error.name}: {this.state.error.message}</p>
                    <pre className="mt-2 whitespace-pre-wrap text-xs">
                      {this.state.error.stack}
                    </pre>
                    {this.state.errorInfo && (
                      <pre className="mt-2 whitespace-pre-wrap text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </div>
                </details>
              )}

              <div className="flex flex-col space-y-2">
                <Button onClick={this.handleRetry} className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={this.handleReload} variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </Button>
                <Button onClick={this.handleGoHome} variant="outline" className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {process.env.NODE_ENV === 'production' && (
                <div className="text-center">
                  <p className="text-xs text-muted-foreground">
                    If this problem persists, please contact support.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Specific error boundaries for different sections
export const AdminErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    onError={(error, errorInfo) => {
      console.error('Admin panel error:', error, errorInfo);
      // Log to admin error tracking service
    }}
    fallback={
      <div className="min-h-[400px] flex items-center justify-center p-4">
        <Card className="w-full max-w-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Bug className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Admin Panel Error</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-muted-foreground">
              There was an error in the admin panel. This has been logged and will be investigated.
            </p>
            <div className="flex flex-col space-y-2">
              <Button onClick={() => window.location.reload()} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Admin Panel
              </Button>
              <Button onClick={() => window.location.href = '/'} variant="outline" className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Go to Homepage
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    }
  >
    {children}
  </ErrorBoundary>
);

export const FormErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    fallback={
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <div className="flex items-center space-x-2 text-red-800">
          <AlertTriangle className="h-4 w-4" />
          <span className="font-medium">Form Error</span>
        </div>
        <p className="text-red-700 text-sm mt-1">
          There was an error with this form. Please refresh the page and try again.
        </p>
        <Button 
          onClick={() => window.location.reload()} 
          variant="outline" 
          size="sm" 
          className="mt-2"
        >
          <RefreshCw className="h-3 w-3 mr-1" />
          Refresh
        </Button>
      </div>
    }
  >
    {children}
  </ErrorBoundary>
);

export const DataErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    fallback={
      <Card>
        <CardContent className="text-center py-8">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Data Loading Error</h3>
          <p className="text-muted-foreground mb-4">
            Unable to load data. Please check your connection and try again.
          </p>
          <Button onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    }
  >
    {children}
  </ErrorBoundary>
);

// Hook for handling async errors in functional components
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: string) => {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error);
    
    // Show user-friendly error message
    if (error.message.includes('network') || error.message.includes('fetch')) {
      toast.error('Network error. Please check your connection and try again.');
    } else if (error.message.includes('unauthorized') || error.message.includes('403')) {
      toast.error('You do not have permission to perform this action.');
    } else if (error.message.includes('not found') || error.message.includes('404')) {
      toast.error('The requested resource was not found.');
    } else {
      toast.error('An unexpected error occurred. Please try again.');
    }

    // Log to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, { context });
    }
  };

  return { handleError };
};

// Async error wrapper for promises
export function withErrorHandling<T>(
  promise: Promise<T>,
  context?: string,
  customErrorHandler?: (error: Error) => void
): Promise<T | null> {
  return (async () => {
    try {
      return await promise;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error');
      
      if (customErrorHandler) {
        customErrorHandler(err);
      } else {
        console.error(`Error${context ? ` in ${context}` : ''}:`, err);
        toast.error('An error occurred. Please try again.');
      }
      
      return null;
    }
  })();
};
