-- Create the admin user in the auth system
-- Since we can't directly insert into auth.users, we'll delete the existing profile
-- and let the user sign up normally, then update their role

-- First, delete the existing profile so it can be recreated
DELETE FROM user_profiles WHERE email = '<EMAIL>';

-- Insert a placeholder that will be updated when the user signs up
INSERT INTO user_profiles (id, email, first_name, last_name, role)
VALUES (
  'dad48961-3fd4-4e6c-a59c-a3a6ffb08bd8'::uuid,
  '<EMAIL>',
  'Admin',
  'User',
  'super_admin'
);

-- Create a function to handle admin user creation
CREATE OR REPLACE FUNCTION create_admin_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- If this is the admin email, set them as super_admin
  IF NEW.email = '<EMAIL>' THEN
    INSERT INTO user_profiles (id, email, first_name, last_name, role)
    VALUES (NEW.id, NEW.email, 'Admin', 'User', 'super_admin')
    ON CONFLICT (id) DO UPDATE SET
      role = 'super_admin',
      email = NEW.email;
  ELSE
    -- For regular users, create a customer profile
    INSERT INTO user_profiles (id, email, first_name, last_name, role)
    VALUES (NEW.id, NEW.email, 
            COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
            COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
            'customer')
    ON CONFLICT (id) DO UPDATE SET
      email = NEW.email,
      first_name = COALESCE(NEW.raw_user_meta_data->>'first_name', user_profiles.first_name),
      last_name = COALESCE(NEW.raw_user_meta_data->>'last_name', user_profiles.last_name);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profiles for new auth users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_admin_profile();