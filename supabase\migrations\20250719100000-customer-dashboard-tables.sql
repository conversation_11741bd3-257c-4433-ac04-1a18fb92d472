-- Customer Dashboard Database Tables
-- This migration creates all tables needed for the customer dashboard functionality

-- 1. Custom Order Requests table
CREATE TABLE IF NOT EXISTS public.custom_order_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    budget_range TEXT,
    preferred_timeline TEXT,
    aquarium_dimensions TEXT,
    aquarium_type TEXT, -- freshwater, saltwater, reef, etc.
    special_requirements TEXT,
    reference_images TEXT[], -- Array of image URLs
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'quote_provided', 'approved', 'in_progress', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    estimated_cost DECIMAL(10,2),
    estimated_timeline TEXT,
    admin_notes TEXT,
    customer_notes TEXT,
    quote_expires_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Customer Messages table (for direct communication)
CREATE TABLE IF NOT EXISTS public.customer_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id UUID NOT NULL, -- Groups messages into conversations
    sender_id UUID REFERENCES auth.users(id),
    recipient_id UUID REFERENCES auth.users(id),
    sender_type TEXT NOT NULL CHECK (sender_type IN ('customer', 'admin')),
    subject TEXT,
    message TEXT NOT NULL,
    attachments TEXT[], -- Array of file URLs
    is_read BOOLEAN DEFAULT false,
    is_important BOOLEAN DEFAULT false,
    related_order_id UUID REFERENCES public.orders(id),
    related_custom_request_id UUID REFERENCES public.custom_order_requests(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Customer Invoices table
CREATE TABLE IF NOT EXISTS public.customer_invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT UNIQUE NOT NULL,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    order_id UUID REFERENCES public.orders(id),
    custom_request_id UUID REFERENCES public.custom_order_requests(id),
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    payment_method TEXT,
    payment_reference TEXT,
    paid_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    terms TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Invoice Line Items table
CREATE TABLE IF NOT EXISTS public.invoice_line_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_id UUID REFERENCES public.customer_invoices(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    product_id UUID REFERENCES public.products(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Customer Addresses table (for shipping/billing)
CREATE TABLE IF NOT EXISTS public.customer_addresses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('shipping', 'billing', 'both')),
    is_default BOOLEAN DEFAULT false,
    label TEXT, -- e.g., "Home", "Office", "Studio"
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    company TEXT,
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    postal_code TEXT NOT NULL,
    country TEXT DEFAULT 'US',
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Customer Payment Methods table
CREATE TABLE IF NOT EXISTS public.customer_payment_methods (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('credit_card', 'debit_card', 'paypal', 'bank_transfer', 'check')),
    is_default BOOLEAN DEFAULT false,
    label TEXT, -- e.g., "Personal Card", "Business Card"
    last_four TEXT, -- Last 4 digits for cards
    card_brand TEXT, -- Visa, Mastercard, etc.
    expiry_month INTEGER,
    expiry_year INTEGER,
    billing_address_id UUID REFERENCES public.customer_addresses(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Customer Notifications table
CREATE TABLE IF NOT EXISTS public.customer_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('order_update', 'custom_request_update', 'invoice', 'message', 'general')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    action_url TEXT,
    is_read BOOLEAN DEFAULT false,
    is_important BOOLEAN DEFAULT false,
    related_order_id UUID REFERENCES public.orders(id),
    related_custom_request_id UUID REFERENCES public.custom_order_requests(id),
    related_invoice_id UUID REFERENCES public.customer_invoices(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Customer Preferences table
CREATE TABLE IF NOT EXISTS public.customer_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    email_notifications BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT false,
    marketing_emails BOOLEAN DEFAULT true,
    order_updates BOOLEAN DEFAULT true,
    custom_request_updates BOOLEAN DEFAULT true,
    newsletter_subscription BOOLEAN DEFAULT true,
    preferred_communication TEXT DEFAULT 'email' CHECK (preferred_communication IN ('email', 'sms', 'phone', 'in_app')),
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_custom_order_requests_user_id ON public.custom_order_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_order_requests_status ON public.custom_order_requests(status);
CREATE INDEX IF NOT EXISTS idx_custom_order_requests_created_at ON public.custom_order_requests(created_at);

CREATE INDEX IF NOT EXISTS idx_customer_messages_conversation_id ON public.customer_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_customer_messages_sender_id ON public.customer_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_customer_messages_recipient_id ON public.customer_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_customer_messages_created_at ON public.customer_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_customer_messages_is_read ON public.customer_messages(is_read);

CREATE INDEX IF NOT EXISTS idx_customer_invoices_user_id ON public.customer_invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_invoices_status ON public.customer_invoices(status);
CREATE INDEX IF NOT EXISTS idx_customer_invoices_invoice_date ON public.customer_invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_customer_invoices_due_date ON public.customer_invoices(due_date);

CREATE INDEX IF NOT EXISTS idx_invoice_line_items_invoice_id ON public.invoice_line_items(invoice_id);

CREATE INDEX IF NOT EXISTS idx_customer_addresses_user_id ON public.customer_addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_type ON public.customer_addresses(type);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_is_default ON public.customer_addresses(is_default);

CREATE INDEX IF NOT EXISTS idx_customer_payment_methods_user_id ON public.customer_payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_payment_methods_is_default ON public.customer_payment_methods(is_default);
CREATE INDEX IF NOT EXISTS idx_customer_payment_methods_is_active ON public.customer_payment_methods(is_active);

CREATE INDEX IF NOT EXISTS idx_customer_notifications_user_id ON public.customer_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_notifications_is_read ON public.customer_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_customer_notifications_created_at ON public.customer_notifications(created_at);

CREATE INDEX IF NOT EXISTS idx_customer_preferences_user_id ON public.customer_preferences(user_id);

-- 10. Create updated_at triggers
CREATE TRIGGER update_custom_order_requests_updated_at BEFORE UPDATE ON public.custom_order_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_messages_updated_at BEFORE UPDATE ON public.customer_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_invoices_updated_at BEFORE UPDATE ON public.customer_invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_addresses_updated_at BEFORE UPDATE ON public.customer_addresses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_payment_methods_updated_at BEFORE UPDATE ON public.customer_payment_methods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_preferences_updated_at BEFORE UPDATE ON public.customer_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. Create functions for generating invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    invoice_number TEXT;
BEGIN
    -- Get the next invoice number
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 5) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.customer_invoices
    WHERE invoice_number ~ '^INV-[0-9]+$';
    
    -- Format as INV-000001
    invoice_number := 'INV-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- 12. Create function to generate conversation IDs
CREATE OR REPLACE FUNCTION generate_conversation_id(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
    conversation_uuid UUID;
BEGIN
    -- Create a deterministic UUID based on the two user IDs
    -- This ensures the same conversation ID for the same pair of users
    SELECT uuid_generate_v5(
        uuid_ns_oid(), 
        LEAST(user1_id::TEXT, user2_id::TEXT) || '-' || GREATEST(user1_id::TEXT, user2_id::TEXT)
    ) INTO conversation_uuid;
    
    RETURN conversation_uuid;
END;
$$ LANGUAGE plpgsql;

-- 13. Insert sample data for testing
-- Insert default customer preferences for existing users
INSERT INTO public.customer_preferences (user_id)
SELECT id FROM public.user_profiles 
WHERE role = 'customer' 
AND id NOT IN (SELECT user_id FROM public.customer_preferences)
ON CONFLICT DO NOTHING;

-- 14. Create RLS policies for customer data (customers can only see their own data)
ALTER TABLE public.custom_order_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_line_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_preferences ENABLE ROW LEVEL SECURITY;

-- Customers can only access their own data
CREATE POLICY "Customers can view their own custom requests" ON public.custom_order_requests FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can insert their own custom requests" ON public.custom_order_requests FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can update their own custom requests" ON public.custom_order_requests FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view their own messages" ON public.customer_messages FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = recipient_id);
CREATE POLICY "Users can send messages" ON public.customer_messages FOR INSERT WITH CHECK (auth.uid() = sender_id);
CREATE POLICY "Users can update their own messages" ON public.customer_messages FOR UPDATE USING (auth.uid() = sender_id);

CREATE POLICY "Customers can view their own invoices" ON public.customer_invoices FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can view their own invoice items" ON public.invoice_line_items FOR SELECT USING (
    invoice_id IN (SELECT id FROM public.customer_invoices WHERE user_id::text = auth.uid()::text)
);

CREATE POLICY "Customers can manage their own addresses" ON public.customer_addresses FOR ALL USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can manage their own payment methods" ON public.customer_payment_methods FOR ALL USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can view their own notifications" ON public.customer_notifications FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can update their own notifications" ON public.customer_notifications FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Customers can manage their own preferences" ON public.customer_preferences FOR ALL USING (auth.uid()::text = user_id::text);

-- Admins can access all customer data
CREATE POLICY "Admins can access all custom requests" ON public.custom_order_requests FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all messages" ON public.customer_messages FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all invoices" ON public.customer_invoices FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all invoice items" ON public.invoice_line_items FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all addresses" ON public.customer_addresses FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all payment methods" ON public.customer_payment_methods FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all notifications" ON public.customer_notifications FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can access all preferences" ON public.customer_preferences FOR ALL USING (
    EXISTS (SELECT 1 FROM public.user_profiles WHERE id::text = auth.uid()::text AND role IN ('admin', 'super_admin'))
);

SELECT 'Customer dashboard database tables created successfully!' as status;
