import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { Upload, X, Image as ImageIcon, AlertCircle, Check } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  bucket: 'product-images' | 'portfolio-images' | 'artist-images' | 'homepage-images';
  currentImageUrl?: string;
  onImageUploaded: (url: string) => void;
  onImageRemoved?: () => void;
  label?: string;
  description?: string;
  maxSizeMB?: number;
  aspectRatio?: 'square' | 'landscape' | 'portrait' | 'auto';
  className?: string;
  required?: boolean;
}

interface UploadState {
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  bucket,
  currentImageUrl,
  onImageUploaded,
  onImageRemoved,
  label = 'Image',
  description = 'Upload an image file (JPEG, PNG, WebP, GIF)',
  maxSizeMB = 10,
  aspectRatio = 'auto',
  className,
  required = false,
}) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    error: null,
    success: false,
  });
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image compression function
  const compressImage = useCallback((file: File, maxWidth: number = 1920, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              resolve(file);
            }
          },
          file.type,
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  // Validate file
  const validateFile = useCallback((file: File): string | null => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    
    if (!allowedTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, WebP, or GIF)';
    }

    if (file.size > maxSizeMB * 1024 * 1024) {
      return `File size must be less than ${maxSizeMB}MB`;
    }

    return null;
  }, [maxSizeMB]);

  // Generate unique filename
  const generateUniqueFilename = useCallback((originalName: string): string => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
    const baseName = originalName.split('.')[0].toLowerCase().replace(/[^a-z0-9]/g, '-');
    return `${baseName}-${timestamp}-${random}.${extension}`;
  }, []);

  // Upload file to Supabase Storage
  const uploadFile = useCallback(async (file: File) => {
    setUploadState({ uploading: true, progress: 0, error: null, success: false });

    try {
      // Validate file
      const validationError = validateFile(file);
      if (validationError) {
        throw new Error(validationError);
      }

      // Compress image
      setUploadState(prev => ({ ...prev, progress: 20 }));
      const compressedFile = await compressImage(file);

      // Generate unique filename
      const filename = generateUniqueFilename(file.name);

      // Upload to Supabase Storage
      setUploadState(prev => ({ ...prev, progress: 50 }));
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filename, compressedFile, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        throw error;
      }

      // Get public URL
      setUploadState(prev => ({ ...prev, progress: 80 }));
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(filename);

      setUploadState(prev => ({ ...prev, progress: 100, success: true }));
      setPreviewUrl(publicUrl);
      onImageUploaded(publicUrl);
      toast.success('Image uploaded successfully!');

      // Reset success state after 2 seconds
      setTimeout(() => {
        setUploadState(prev => ({ ...prev, success: false, uploading: false, progress: 0 }));
      }, 2000);

    } catch (error: any) {
      console.error('Upload error:', error);
      setUploadState({
        uploading: false,
        progress: 0,
        error: error.message || 'Failed to upload image',
        success: false,
      });
      toast.error(error.message || 'Failed to upload image');
    }
  }, [bucket, validateFile, compressImage, generateUniqueFilename, onImageUploaded]);

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (files && files.length > 0) {
      uploadFile(files[0]);
    }
  }, [uploadFile]);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  // Handle remove image
  const handleRemoveImage = useCallback(async () => {
    if (previewUrl && onImageRemoved) {
      setPreviewUrl(null);
      onImageRemoved();
      toast.success('Image removed');
    }
  }, [previewUrl, onImageRemoved]);

  // Get aspect ratio class
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square': return 'aspect-square';
      case 'landscape': return 'aspect-video';
      case 'portrait': return 'aspect-[3/4]';
      default: return 'aspect-video';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          {previewUrl ? (
            // Image preview
            <div className="relative group">
              <div className={cn('relative overflow-hidden bg-muted', getAspectRatioClass())}>
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={uploadState.uploading}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      Replace
                    </Button>
                    {onImageRemoved && (
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={handleRemoveImage}
                        disabled={uploadState.uploading}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Remove
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Upload area
            <div
              className={cn(
                'border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center transition-colors',
                dragActive && 'border-primary bg-primary/5',
                uploadState.uploading && 'pointer-events-none opacity-50',
                getAspectRatioClass()
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center justify-center h-full space-y-4">
                {uploadState.uploading ? (
                  <>
                    <Upload className="h-8 w-8 text-primary animate-pulse" />
                    <div className="space-y-2 w-full max-w-xs">
                      <p className="text-sm text-muted-foreground">Uploading...</p>
                      <Progress value={uploadState.progress} className="w-full" />
                      <p className="text-xs text-muted-foreground">{uploadState.progress}%</p>
                    </div>
                  </>
                ) : uploadState.success ? (
                  <>
                    <Check className="h-8 w-8 text-green-500" />
                    <p className="text-sm text-green-600">Upload successful!</p>
                  </>
                ) : (
                  <>
                    <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    <div className="space-y-2">
                      <p className="text-sm font-medium">
                        Drag and drop an image here, or click to select
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Maximum file size: {maxSizeMB}MB
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={uploadState.uploading}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Select Image
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}

          {uploadState.error && (
            <div className="p-4 bg-destructive/10 border-t border-destructive/20">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <p className="text-sm">{uploadState.error}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp,image/gif"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
};
