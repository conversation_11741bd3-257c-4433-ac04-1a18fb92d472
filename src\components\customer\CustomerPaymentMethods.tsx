import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2, 
  Star,
  Save,
  X,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface PaymentMethod {
  id?: string;
  user_id: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer' | 'check';
  is_default: boolean;
  label?: string;
  last_four?: string;
  card_brand?: string;
  expiry_month?: number;
  expiry_year?: number;
  billing_address_id?: string;
  is_active: boolean;
}

interface CustomerAddress {
  id: string;
  label?: string;
  first_name: string;
  last_name: string;
  address_line_1: string;
  city: string;
  state: string;
  postal_code: string;
}

const emptyPaymentMethod: PaymentMethod = {
  user_id: '',
  type: 'credit_card',
  is_default: false,
  label: '',
  last_four: '',
  card_brand: '',
  expiry_month: undefined,
  expiry_year: undefined,
  billing_address_id: undefined,
  is_active: true
};

export const CustomerPaymentMethods = () => {
  const { user } = useAuth();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [addresses, setAddresses] = useState<CustomerAddress[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [editingPayment, setEditingPayment] = useState<PaymentMethod | null>(null);
  const [formData, setFormData] = useState<PaymentMethod>(emptyPaymentMethod);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      fetchPaymentMethods();
      fetchAddresses();
    }
  }, [user]);

  const fetchPaymentMethods = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('customer_payment_methods')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPaymentMethods(data || []);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      toast.error('Failed to fetch payment methods');
    } finally {
      setLoading(false);
    }
  };

  const fetchAddresses = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('customer_addresses')
        .select('id, label, first_name, last_name, address_line_1, city, state, postal_code')
        .eq('user_id', user.id)
        .in('type', ['billing', 'both']);

      if (error) throw error;
      setAddresses(data || []);
    } catch (error) {
      console.error('Error fetching addresses:', error);
    }
  };

  const openPaymentDialog = (payment?: PaymentMethod) => {
    if (payment) {
      setEditingPayment(payment);
      setFormData(payment);
    } else {
      setEditingPayment(null);
      setFormData({ ...emptyPaymentMethod, user_id: user?.id || '' });
    }
    setShowPaymentDialog(true);
  };

  const closePaymentDialog = () => {
    setShowPaymentDialog(false);
    setEditingPayment(null);
    setFormData(emptyPaymentMethod);
  };

  const savePaymentMethod = async () => {
    if (!user) return;

    try {
      setSaving(true);

      // Validate required fields based on payment type
      if (formData.type === 'credit_card' || formData.type === 'debit_card') {
        if (!formData.last_four || !formData.card_brand || !formData.expiry_month || !formData.expiry_year) {
          toast.error('Please fill in all card details');
          return;
        }
      }

      // If setting as default, remove default from other payment methods
      if (formData.is_default) {
        await supabase
          .from('customer_payment_methods')
          .update({ is_default: false })
          .eq('user_id', user.id);
      }

      if (editingPayment) {
        // Update existing payment method
        const { error } = await supabase
          .from('customer_payment_methods')
          .update(formData)
          .eq('id', editingPayment.id);

        if (error) throw error;
        toast.success('Payment method updated successfully!');
      } else {
        // Create new payment method
        const { error } = await supabase
          .from('customer_payment_methods')
          .insert(formData);

        if (error) throw error;
        toast.success('Payment method added successfully!');
      }

      closePaymentDialog();
      fetchPaymentMethods();
    } catch (error: any) {
      console.error('Error saving payment method:', error);
      toast.error(`Failed to save payment method: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const deletePaymentMethod = async (paymentId: string) => {
    if (!confirm('Are you sure you want to delete this payment method?')) return;

    try {
      const { error } = await supabase
        .from('customer_payment_methods')
        .update({ is_active: false })
        .eq('id', paymentId);

      if (error) throw error;
      toast.success('Payment method deleted successfully!');
      fetchPaymentMethods();
    } catch (error: any) {
      console.error('Error deleting payment method:', error);
      toast.error(`Failed to delete payment method: ${error.message}`);
    }
  };

  const setAsDefault = async (paymentId: string) => {
    if (!user) return;

    try {
      // Remove default from all payment methods
      await supabase
        .from('customer_payment_methods')
        .update({ is_default: false })
        .eq('user_id', user.id);

      // Set the selected payment method as default
      const { error } = await supabase
        .from('customer_payment_methods')
        .update({ is_default: true })
        .eq('id', paymentId);

      if (error) throw error;
      toast.success('Default payment method updated!');
      fetchPaymentMethods();
    } catch (error: any) {
      console.error('Error setting default payment method:', error);
      toast.error(`Failed to set default payment method: ${error.message}`);
    }
  };

  const getPaymentTypeIcon = (type: string) => {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const getCardBrandColor = (brand: string) => {
    const colors: Record<string, string> = {
      visa: 'text-blue-600',
      mastercard: 'text-red-600',
      amex: 'text-green-600',
      discover: 'text-orange-600'
    };
    return colors[brand?.toLowerCase()] || 'text-gray-600';
  };

  const formatExpiryDate = (month?: number, year?: number) => {
    if (!month || !year) return '';
    return `${month.toString().padStart(2, '0')}/${year.toString().slice(-2)}`;
  };

  if (loading) {
    return <div className="text-center py-8">Loading your payment methods...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Payment Methods</h2>
          <p className="text-muted-foreground">
            Manage your payment methods for orders and invoices
          </p>
        </div>
        <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => openPaymentDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingPayment ? 'Edit Payment Method' : 'Add New Payment Method'}
              </DialogTitle>
            </DialogHeader>
            <PaymentMethodForm 
              paymentMethod={formData}
              setPaymentMethod={setFormData}
              addresses={addresses}
              onSave={savePaymentMethod}
              onCancel={closePaymentDialog}
              saving={saving}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Security Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="flex items-start gap-3 pt-6">
          <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900">Secure Payment Processing</h3>
            <p className="text-sm text-blue-700 mt-1">
              Your payment information is encrypted and securely stored. We never store your full card numbers.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {paymentMethods.length > 0 ? (
          paymentMethods.map((payment) => (
            <Card key={payment.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getPaymentTypeIcon(payment.type)}
                    <CardTitle className="text-lg">
                      {payment.label || `${payment.type.replace('_', ' ')}`}
                    </CardTitle>
                    {payment.is_default && (
                      <Badge variant="default" className="text-xs">
                        <Star className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm">
                  {(payment.type === 'credit_card' || payment.type === 'debit_card') && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Card Number:</span>
                        <span className="font-mono">•••• •••• •••• {payment.last_four}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Brand:</span>
                        <span className={`font-medium capitalize ${getCardBrandColor(payment.card_brand || '')}`}>
                          {payment.card_brand}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Expires:</span>
                        <span>{formatExpiryDate(payment.expiry_month, payment.expiry_year)}</span>
                      </div>
                    </>
                  )}
                  {payment.type === 'paypal' && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Type:</span>
                      <span className="font-medium">PayPal Account</span>
                    </div>
                  )}
                  {payment.type === 'bank_transfer' && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Type:</span>
                      <span className="font-medium">Bank Transfer</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openPaymentDialog(payment)}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  {!payment.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAsDefault(payment.id!)}
                    >
                      <Star className="h-3 w-3 mr-1" />
                      Set Default
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deletePaymentMethod(payment.id!)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full">
            <Card>
              <CardContent className="text-center py-12">
                <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No payment methods yet</h3>
                <p className="text-muted-foreground mb-4">
                  Add a payment method to make purchases and pay invoices.
                </p>
                <Button onClick={() => openPaymentDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Payment Method
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

// Payment Method Form Component
const PaymentMethodForm = ({ 
  paymentMethod, 
  setPaymentMethod, 
  addresses,
  onSave, 
  onCancel, 
  saving 
}: {
  paymentMethod: PaymentMethod;
  setPaymentMethod: (payment: PaymentMethod) => void;
  addresses: CustomerAddress[];
  onSave: () => void;
  onCancel: () => void;
  saving: boolean;
}) => {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 20 }, (_, i) => currentYear + i);
  const months = Array.from({ length: 12 }, (_, i) => i + 1);

  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="type">Payment Type</Label>
          <Select
            value={paymentMethod.type}
            onValueChange={(value: PaymentMethod['type']) => 
              setPaymentMethod({ ...paymentMethod, type: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="credit_card">Credit Card</SelectItem>
              <SelectItem value="debit_card">Debit Card</SelectItem>
              <SelectItem value="paypal">PayPal</SelectItem>
              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
              <SelectItem value="check">Check</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="label">Label (Optional)</Label>
          <Input
            id="label"
            value={paymentMethod.label || ''}
            onChange={(e) => setPaymentMethod({ ...paymentMethod, label: e.target.value })}
            placeholder="e.g., Personal Card, Business Card"
          />
        </div>

        {(paymentMethod.type === 'credit_card' || paymentMethod.type === 'debit_card') && (
          <>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="card_brand">Card Brand</Label>
                <Select
                  value={paymentMethod.card_brand || ''}
                  onValueChange={(value) => 
                    setPaymentMethod({ ...paymentMethod, card_brand: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select card brand" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="visa">Visa</SelectItem>
                    <SelectItem value="mastercard">Mastercard</SelectItem>
                    <SelectItem value="amex">American Express</SelectItem>
                    <SelectItem value="discover">Discover</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="last_four">Last 4 Digits</Label>
                <Input
                  id="last_four"
                  value={paymentMethod.last_four || ''}
                  onChange={(e) => setPaymentMethod({ ...paymentMethod, last_four: e.target.value })}
                  placeholder="1234"
                  maxLength={4}
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="expiry_month">Expiry Month</Label>
                <Select
                  value={paymentMethod.expiry_month?.toString() || ''}
                  onValueChange={(value) => 
                    setPaymentMethod({ ...paymentMethod, expiry_month: parseInt(value) })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map(month => (
                      <SelectItem key={month} value={month.toString()}>
                        {month.toString().padStart(2, '0')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="expiry_year">Expiry Year</Label>
                <Select
                  value={paymentMethod.expiry_year?.toString() || ''}
                  onValueChange={(value) => 
                    setPaymentMethod({ ...paymentMethod, expiry_year: parseInt(value) })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="billing_address">Billing Address</Label>
              <Select
                value={paymentMethod.billing_address_id || ''}
                onValueChange={(value) => 
                  setPaymentMethod({ ...paymentMethod, billing_address_id: value || undefined })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select billing address" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No billing address</SelectItem>
                  {addresses.map(address => (
                    <SelectItem key={address.id} value={address.id}>
                      {address.label || `${address.first_name} ${address.last_name}`} - {address.address_line_1}, {address.city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        )}

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="is_default"
            checked={paymentMethod.is_default}
            onChange={(e) => setPaymentMethod({ ...paymentMethod, is_default: e.target.checked })}
            className="rounded"
          />
          <Label htmlFor="is_default" className="text-sm">
            Set as default payment method
          </Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={onSave} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Payment Method'}
        </Button>
      </div>
    </div>
  );
};
