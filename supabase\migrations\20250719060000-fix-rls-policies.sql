-- Temporarily disable <PERSON><PERSON> on user_profiles to fix circular dependency
-- This allows the auth system to work properly
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;

-- Re-enable R<PERSON> with simpler policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that might cause circular dependencies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON public.user_profiles;

-- Create simple policies that allow basic operations
-- Allow authenticated users to view all profiles (needed for admin checks)
CREATE POLICY "Authenticated users can view profiles" ON public.user_profiles
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

-- Allow users to insert their own profile
CREATE POLICY "Users can insert own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow authenticated users to delete profiles (admins will handle this in app logic)
CREATE POLICY "Authenticated users can delete profiles" ON public.user_profiles
  FOR DELETE USING (auth.role() = 'authenticated');
