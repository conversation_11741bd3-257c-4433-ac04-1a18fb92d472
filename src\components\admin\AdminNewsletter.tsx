import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { Plus, Mail, Users, Download } from 'lucide-react';
import { toast } from 'sonner';

interface NewsletterSubscriber {
  id: string;
  email: string;
  name?: string;
  status: 'active' | 'unsubscribed' | 'bounced';
  subscribed_at: string;
  unsubscribed_at?: string;
  tags?: string[];
  created_at: string;
}

export const AdminNewsletter = () => {
  const [subscribers, setSubscribers] = useState<NewsletterSubscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const [formData, setFormData] = useState({
    email: '',
    name: '',
    status: 'active' as const
  });

  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    unsubscribed: 0,
    bounced: 0
  });

  useEffect(() => {
    fetchSubscribers();
  }, []);

  const fetchSubscribers = async () => {
    try {
      const { data, error } = await supabase
        .from('newsletter_subscribers')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const subscriberData = (data || []).map(sub => ({
        ...sub,
        status: sub.status as 'active' | 'unsubscribed' | 'bounced'
      }));
      setSubscribers(subscriberData);

      // Calculate stats
      const newStats = {
        total: subscriberData.length,
        active: subscriberData.filter(s => s.status === 'active').length,
        unsubscribed: subscriberData.filter(s => s.status === 'unsubscribed').length,
        bounced: subscriberData.filter(s => s.status === 'bounced').length
      };
      setStats(newStats);
    } catch (error) {
      console.error('Error fetching subscribers:', error);
      toast.error('Failed to fetch subscribers');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSubscriber = async () => {
    try {
      const { error } = await supabase
        .from('newsletter_subscribers')
        .insert({
          ...formData,
          subscribed_at: new Date().toISOString()
        });

      if (error) throw error;
      
      toast.success('Subscriber added successfully!');
      fetchSubscribers();
      resetForm();
    } catch (error: any) {
      console.error('Error adding subscriber:', error);
      if (error.code === '23505') {
        toast.error('Email already exists in the newsletter');
      } else {
        toast.error('Failed to add subscriber');
      }
    }
  };

  const handleUpdateStatus = async (id: string, newStatus: string) => {
    try {
      const updateData: any = { 
        status: newStatus 
      };

      if (newStatus === 'unsubscribed') {
        updateData.unsubscribed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('newsletter_subscribers')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;
      
      toast.success('Subscriber status updated!');
      fetchSubscribers();
    } catch (error) {
      console.error('Error updating subscriber status:', error);
      toast.error('Failed to update subscriber status');
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      name: '',
      status: 'active'
    });
    setShowAddForm(false);
  };

  const exportSubscribers = () => {
    const activeSubscribers = subscribers.filter(s => s.status === 'active');
    const csvContent = 'data:text/csv;charset=utf-8,' + 
      'Email,Name,Status,Subscribed Date\n' +
      activeSubscribers.map(s => 
        `${s.email},"${s.name || ''}",${s.status},${new Date(s.subscribed_at).toLocaleDateString()}`
      ).join('\n');

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const filteredSubscribers = filterStatus === 'all' 
    ? subscribers 
    : subscribers.filter(s => s.status === filterStatus);

  if (loading) {
    return <div className="text-center py-8">Loading newsletter data...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Newsletter Management</h2>
          <p className="text-muted-foreground">
            Manage newsletter subscribers and campaigns
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportSubscribers}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Subscriber
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Total Subscribers</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Unsubscribed</p>
                <p className="text-2xl font-bold text-orange-600">{stats.unsubscribed}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Bounced</p>
                <p className="text-2xl font-bold text-red-600">{stats.bounced}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Subscriber Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Subscriber</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Optional name"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleAddSubscriber} disabled={!formData.email}>
                Add Subscriber
              </Button>
              <Button variant="outline" onClick={resetForm}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-4">
          <div className="flex items-center gap-4">
            <Label>Filter by status:</Label>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All ({stats.total})</SelectItem>
                <SelectItem value="active">Active ({stats.active})</SelectItem>
                <SelectItem value="unsubscribed">Unsubscribed ({stats.unsubscribed})</SelectItem>
                <SelectItem value="bounced">Bounced ({stats.bounced})</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Subscribers List */}
      <Card>
        <CardHeader>
          <CardTitle>Subscribers ({filteredSubscribers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredSubscribers.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {filterStatus === 'all' ? 'No subscribers yet' : `No ${filterStatus} subscribers`}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredSubscribers.map((subscriber) => (
                <div key={subscriber.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{subscriber.email}</span>
                      {subscriber.name && (
                        <span className="text-sm text-muted-foreground">({subscriber.name})</span>
                      )}
                      <Badge variant={
                        subscriber.status === 'active' ? 'default' :
                        subscriber.status === 'unsubscribed' ? 'secondary' : 'destructive'
                      }>
                        {subscriber.status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Subscribed: {new Date(subscriber.subscribed_at).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <div className="flex gap-2">
                    {subscriber.status === 'active' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateStatus(subscriber.id, 'unsubscribed')}
                      >
                        Unsubscribe
                      </Button>
                    )}
                    {subscriber.status === 'unsubscribed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateStatus(subscriber.id, 'active')}
                      >
                        Reactivate
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};