-- Fix admin form save issues
-- This migration addresses database schema and RLS policy issues that prevent admin forms from saving

-- 1. Fix site_settings table data type and existing data
-- Handle conversion from text to JSONB properly
DO $$
BEGIN
    -- Check if the column exists and is text type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'site_settings'
        AND column_name = 'value'
        AND data_type = 'text'
    ) THEN
        -- First, fix existing data that isn't properly JSON formatted
        UPDATE public.site_settings
        SET value = '"' || value || '"'
        WHERE value IS NOT NULL
        AND value != 'true'
        AND value != 'false'
        AND value !~ '^[0-9]+\.?[0-9]*$'  -- not a number
        AND value !~ '^".*"$'             -- not already quoted
        AND value !~ '^\{.*\}$'           -- not an object
        AND value !~ '^\[.*\]$';          -- not an array

        -- Now convert the column type to JSONB
        ALTER TABLE public.site_settings ALTER COLUMN value TYPE JSONB USING value::JSONB;
    END IF;
END $$;

-- 2. Ensure all required tables have proper RLS policies for admin operations
-- Products table policies
DROP POLICY IF EXISTS "Admins can manage products" ON public.products;
CREATE POLICY "Admins can manage products" ON public.products
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'super_admin')
    )
  );

-- Portfolio items table policies  
DROP POLICY IF EXISTS "Admins can manage portfolio items" ON public.portfolio_items;
CREATE POLICY "Admins can manage portfolio items" ON public.portfolio_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'super_admin')
    )
  );

-- Artist profile table policies
DROP POLICY IF EXISTS "Admins can manage artist profile" ON public.artist_profile;
CREATE POLICY "Admins can manage artist profile" ON public.artist_profile
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'super_admin')
    )
  );

-- Site settings table policies
DROP POLICY IF EXISTS "Admins can manage site settings" ON public.site_settings;
CREATE POLICY "Admins can manage site settings" ON public.site_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'super_admin')
    )
  );

-- 3. Add public read policies for tables that need to be displayed on public pages
-- Products public read policy
DROP POLICY IF EXISTS "Products are publicly readable" ON public.products;
CREATE POLICY "Products are publicly readable" ON public.products
  FOR SELECT USING (true);

-- Portfolio items public read policy
DROP POLICY IF EXISTS "Portfolio items are publicly readable" ON public.portfolio_items;
CREATE POLICY "Portfolio items are publicly readable" ON public.portfolio_items
  FOR SELECT USING (true);

-- Artist profile public read policy
DROP POLICY IF EXISTS "Artist profile is publicly readable" ON public.artist_profile;
CREATE POLICY "Artist profile is publicly readable" ON public.artist_profile
  FOR SELECT USING (true);

-- Site settings public read policy (for homepage content)
DROP POLICY IF EXISTS "Site settings are publicly readable" ON public.site_settings;
CREATE POLICY "Site settings are publicly readable" ON public.site_settings
  FOR SELECT USING (true);

-- 4. Ensure all tables have RLS enabled
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;

-- 5. Add helpful indexes for admin queries
CREATE INDEX IF NOT EXISTS idx_products_featured ON public.products(featured);
CREATE INDEX IF NOT EXISTS idx_products_category ON public.products(category);
CREATE INDEX IF NOT EXISTS idx_portfolio_featured ON public.portfolio_items(featured);
CREATE INDEX IF NOT EXISTS idx_portfolio_sort_order ON public.portfolio_items(sort_order);
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON public.site_settings(key);

-- 6. Add constraints to ensure data integrity
-- Ensure product prices are positive
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'products_price_positive' 
        AND table_name = 'products'
    ) THEN
        ALTER TABLE public.products ADD CONSTRAINT products_price_positive CHECK (price > 0);
    END IF;
END $$;

-- Ensure portfolio sort_order is non-negative
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'portfolio_sort_order_non_negative' 
        AND table_name = 'portfolio_items'
    ) THEN
        ALTER TABLE public.portfolio_items ADD CONSTRAINT portfolio_sort_order_non_negative CHECK (sort_order >= 0);
    END IF;
END $$;

-- 7. Create function to help debug admin permissions
CREATE OR REPLACE FUNCTION check_admin_permissions(user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
    table_name TEXT,
    can_select BOOLEAN,
    can_insert BOOLEAN,
    can_update BOOLEAN,
    can_delete BOOLEAN,
    user_role TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::TEXT,
        has_table_privilege(user_id, 'public.' || t.table_name, 'SELECT') as can_select,
        has_table_privilege(user_id, 'public.' || t.table_name, 'INSERT') as can_insert,
        has_table_privilege(user_id, 'public.' || t.table_name, 'UPDATE') as can_update,
        has_table_privilege(user_id, 'public.' || t.table_name, 'DELETE') as can_delete,
        COALESCE(up.role::TEXT, 'not_found') as user_role
    FROM (
        VALUES 
            ('products'),
            ('portfolio_items'),
            ('artist_profile'),
            ('site_settings')
    ) AS t(table_name)
    LEFT JOIN public.user_profiles up ON up.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the debug function
GRANT EXECUTE ON FUNCTION check_admin_permissions TO authenticated;
