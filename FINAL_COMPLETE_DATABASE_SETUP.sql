-- FINAL COMPLETE DATABASE SETUP
-- This is the comprehensive database setup for the complete admin system
-- Run this after the initial database fix to add all new features

-- 1. Ensure all base tables exist with proper structure
CREATE TABLE IF NOT EXISTS public.products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category TEXT NOT NULL,
    image_url TEXT,
    material TEXT,
    size TEXT,
    in_stock BOOLEAN DEFAULT true,
    featured BOOLEAN DEFAULT false,
    stock_quantity INTEGER DEFAULT 0,
    sku TEXT,
    meta_title TEXT,
    meta_description TEXT,
    tags TEXT[],
    weight DECIMAL(8,2),
    dimensions TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.portfolio_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    category TEXT NOT NULL,
    featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.artist_profile (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    bio TEXT,
    full_biography TEXT,
    portrait_url TEXT,
    hero_image_url TEXT,
    website_url TEXT,
    social_instagram TEXT,
    social_facebook TEXT,
    years_experience INTEGER DEFAULT 0,
    total_pieces_created INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.site_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    first_name TEXT,
    last_name TEXT,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    role TEXT DEFAULT 'customer' CHECK (role IN ('customer', 'admin', 'super_admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_number TEXT UNIQUE NOT NULL,
    user_id UUID REFERENCES public.user_profiles(id),
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    shipping_method TEXT,
    tracking_number TEXT,
    notes TEXT,
    internal_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create all new tables for enhanced features
CREATE TABLE IF NOT EXISTS public.product_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    slug TEXT NOT NULL UNIQUE,
    parent_id UUID REFERENCES public.product_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.product_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    sku TEXT UNIQUE,
    price DECIMAL(10,2),
    stock_quantity INTEGER DEFAULT 0,
    variant_options JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.product_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.order_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.order_addresses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('billing', 'shipping')),
    first_name TEXT,
    last_name TEXT,
    company TEXT,
    address_line_1 TEXT,
    address_line_2 TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'US',
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.order_status_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    notes TEXT,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.blog_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT DEFAULT '#3B82F6',
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image_url TEXT,
    author_id UUID REFERENCES auth.users(id),
    category_id UUID REFERENCES public.blog_categories(id),
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    featured BOOLEAN DEFAULT false,
    meta_title TEXT,
    meta_description TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.faq_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.faqs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category_id UUID REFERENCES public.faq_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.contact_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT,
    message TEXT NOT NULL,
    phone TEXT,
    status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_to UUID REFERENCES auth.users(id),
    responded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.newsletter_subscribers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    name TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
    source TEXT,
    tags TEXT[],
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.admin_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create all indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_category ON public.products(category);
CREATE INDEX IF NOT EXISTS idx_products_featured ON public.products(featured);
CREATE INDEX IF NOT EXISTS idx_products_in_stock ON public.products(in_stock);
CREATE INDEX IF NOT EXISTS idx_products_sort_order ON public.products(sort_order);
CREATE INDEX IF NOT EXISTS idx_portfolio_category ON public.portfolio_items(category);
CREATE INDEX IF NOT EXISTS idx_portfolio_featured ON public.portfolio_items(featured);
CREATE INDEX IF NOT EXISTS idx_portfolio_sort_order ON public.portfolio_items(sort_order);
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON public.site_settings(key);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON public.orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON public.orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON public.orders(created_at);
CREATE INDEX IF NOT EXISTS idx_product_categories_parent_id ON public.product_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_product_categories_slug ON public.product_categories(slug);
CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON public.product_variants(product_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_sku ON public.product_variants(sku);
CREATE INDEX IF NOT EXISTS idx_product_images_product_id ON public.product_images(product_id);
CREATE INDEX IF NOT EXISTS idx_product_images_is_primary ON public.product_images(is_primary);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON public.order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_addresses_order_id ON public.order_addresses(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON public.order_status_history(order_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON public.blog_posts(status);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON public.blog_posts(published_at);
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON public.blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_category_id ON public.blog_posts(category_id);
CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON public.blog_categories(slug);
CREATE INDEX IF NOT EXISTS idx_faqs_category_id ON public.faqs(category_id);
CREATE INDEX IF NOT EXISTS idx_faqs_is_active ON public.faqs(is_active);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON public.contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON public.contact_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_email ON public.newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_status ON public.newsletter_subscribers(status);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_user_id ON public.admin_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON public.admin_activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_resource_type ON public.admin_activity_logs(resource_type);

-- 4. Insert comprehensive default data
INSERT INTO public.product_categories (name, description, slug) VALUES
    ('Decorations', 'Aquarium decorative items and ornaments', 'decorations'),
    ('Sculptures', 'Artistic sculptures for aquariums', 'sculptures'),
    ('Plants', 'Artificial and live plants for aquascaping', 'plants'),
    ('Lighting', 'Aquarium lighting solutions and accessories', 'lighting'),
    ('Accessories', 'Various aquarium accessories and tools', 'accessories'),
    ('Custom Work', 'Bespoke custom aquarium installations', 'custom-work')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.blog_categories (name, slug, description, color) VALUES
    ('Aquarium Care', 'aquarium-care', 'Tips and guides for aquarium maintenance', '#10B981'),
    ('Design Ideas', 'design-ideas', 'Creative aquarium design inspiration', '#3B82F6'),
    ('Product Spotlights', 'product-spotlights', 'Featured products and their uses', '#F59E0B'),
    ('Artist Stories', 'artist-stories', 'Behind the scenes and artist insights', '#8B5CF6'),
    ('Tutorials', 'tutorials', 'Step-by-step guides and how-tos', '#EF4444'),
    ('News & Updates', 'news-updates', 'Latest news and company updates', '#06B6D4')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.faq_categories (name, slug, description) VALUES
    ('General', 'general', 'General questions about our products and services'),
    ('Shipping & Delivery', 'shipping-delivery', 'Questions about shipping, delivery, and returns'),
    ('Custom Orders', 'custom-orders', 'Information about custom work and commissions'),
    ('Care & Maintenance', 'care-maintenance', 'How to care for and maintain your aquarium decor'),
    ('Installation', 'installation', 'Installation guides and requirements'),
    ('Pricing & Payment', 'pricing-payment', 'Questions about pricing, payment, and billing')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.site_settings (key, value, description) VALUES
    ('site_name', '"Depths of Perception"', 'Website name'),
    ('site_description', '"Custom aquarium decorations by a renowned sculptor"', 'Website description'),
    ('hero_title', '"Depths of Perception"', 'Homepage hero title'),
    ('hero_subtitle', '"Custom Aquarium Decor from the Depths of Imagination"', 'Homepage hero subtitle'),
    ('hero_image_url', '""', 'Homepage hero background image'),
    ('about_title', '"About the Artist"', 'About section title'),
    ('about_description', '"Creating unique aquarium decorations and sculptures that transform underwater environments into works of art."', 'About section description'),
    ('about_image_url', '""', 'About section image'),
    ('featured_portfolio_title', '"Featured Artwork"', 'Featured portfolio section title'),
    ('featured_products_title', '"Featured Products"', 'Featured products section title'),
    ('contact_cta_title', '"Ready to Transform Your Aquarium?"', 'Contact CTA title'),
    ('contact_cta_description', '"Let\'s create something extraordinary together. Get in touch to discuss your custom aquarium project."', 'Contact CTA description'),
    ('company_email', '"<EMAIL>"', 'Company contact email'),
    ('company_phone', '""', 'Company contact phone'),
    ('company_address', '""', 'Company address'),
    ('social_instagram', '""', 'Instagram URL'),
    ('social_facebook', '""', 'Facebook URL'),
    ('social_twitter', '""', 'Twitter URL'),
    ('google_analytics_id', '""', 'Google Analytics tracking ID'),
    ('meta_keywords', '"aquarium decor, custom sculptures, aquascaping, underwater art"', 'Default meta keywords')
ON CONFLICT (key) DO NOTHING;

-- 5. Create updated_at triggers for all tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at columns
DO $$
DECLARE
    table_name TEXT;
    tables_with_updated_at TEXT[] := ARRAY[
        'products', 'portfolio_items', 'artist_profile', 'site_settings', 
        'user_profiles', 'orders', 'product_categories', 'product_variants', 
        'product_images', 'order_items', 'order_addresses', 'blog_categories', 
        'blog_posts', 'faq_categories', 'faqs', 'contact_submissions', 
        'newsletter_subscribers'
    ];
BEGIN
    FOREACH table_name IN ARRAY tables_with_updated_at
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%I_updated_at ON public.%I', table_name, table_name);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at BEFORE UPDATE ON public.%I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', table_name, table_name);
    END LOOP;
END $$;

-- 6. Insert sample artist profile if none exists
INSERT INTO public.artist_profile (name, bio, full_biography, years_experience, total_pieces_created) 
SELECT 
    'Master Sculptor',
    'Renowned artist specializing in custom aquarium decorations and underwater sculptures.',
    'With over 15 years of experience in sculpting and aquascaping, our master artist has created hundreds of unique pieces that transform ordinary aquariums into extraordinary underwater worlds. Each piece is carefully crafted to not only enhance the aesthetic appeal of your aquarium but also provide a safe and natural environment for your aquatic life.',
    15,
    500
WHERE NOT EXISTS (SELECT 1 FROM public.artist_profile LIMIT 1);

-- 7. Create sample FAQs
INSERT INTO public.faqs (question, answer, category_id, sort_order) 
SELECT 
    'How long does it take to create a custom piece?',
    'Custom pieces typically take 2-4 weeks to complete, depending on the complexity and size of the project. We will provide you with a detailed timeline when we discuss your specific requirements.',
    (SELECT id FROM public.faq_categories WHERE slug = 'custom-orders' LIMIT 1),
    1
WHERE NOT EXISTS (SELECT 1 FROM public.faqs WHERE question LIKE '%How long does it take%');

INSERT INTO public.faqs (question, answer, category_id, sort_order) 
SELECT 
    'Are your decorations safe for fish?',
    'Absolutely! All our decorations are made from aquarium-safe materials that won''t harm your fish or affect water chemistry. We use non-toxic materials and sealers specifically designed for aquarium use.',
    (SELECT id FROM public.faq_categories WHERE slug = 'general' LIMIT 1),
    2
WHERE NOT EXISTS (SELECT 1 FROM public.faqs WHERE question LIKE '%safe for fish%');

INSERT INTO public.faqs (question, answer, category_id, sort_order) 
SELECT 
    'Do you ship internationally?',
    'Yes, we ship worldwide! Shipping costs and delivery times vary by location. Please contact us for a shipping quote to your specific location.',
    (SELECT id FROM public.faq_categories WHERE slug = 'shipping-delivery' LIMIT 1),
    3
WHERE NOT EXISTS (SELECT 1 FROM public.faqs WHERE question LIKE '%ship internationally%');

-- 8. Final verification
SELECT 'Complete database setup finished successfully!' as status;

-- Show table counts to verify everything was created
SELECT 
    'products: ' || COUNT(*) as table_info FROM public.products
UNION ALL SELECT 'portfolio_items: ' || COUNT(*) FROM public.portfolio_items
UNION ALL SELECT 'artist_profile: ' || COUNT(*) FROM public.artist_profile
UNION ALL SELECT 'site_settings: ' || COUNT(*) FROM public.site_settings
UNION ALL SELECT 'user_profiles: ' || COUNT(*) FROM public.user_profiles
UNION ALL SELECT 'orders: ' || COUNT(*) FROM public.orders
UNION ALL SELECT 'product_categories: ' || COUNT(*) FROM public.product_categories
UNION ALL SELECT 'blog_categories: ' || COUNT(*) FROM public.blog_categories
UNION ALL SELECT 'blog_posts: ' || COUNT(*) FROM public.blog_posts
UNION ALL SELECT 'faq_categories: ' || COUNT(*) FROM public.faq_categories
UNION ALL SELECT 'faqs: ' || COUNT(*) FROM public.faqs
UNION ALL SELECT 'contact_submissions: ' || COUNT(*) FROM public.contact_submissions
UNION ALL SELECT 'newsletter_subscribers: ' || COUNT(*) FROM public.newsletter_subscribers;
