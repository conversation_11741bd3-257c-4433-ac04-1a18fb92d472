-- Add Missing E-commerce Tables to Complete Aquarium Database
-- This migration adds only the missing tables to the existing well-structured database
-- 
-- EXISTING TABLES (13): products, orders, order_items, order_addresses, order_tracking, 
-- user_profiles, portfolio_items, artist_profile, blog_posts, contact_submissions, 
-- newsletter_subscribers, faq_entries, site_settings
--
-- ADDING (15 new tables): product_categories, product_variants, product_images, 
-- shopping_carts, cart_items, wishlist_items, customer_addresses, blog_categories, 
-- faq_categories, faqs, custom_order_requests, custom_order_quotes, 
-- maintenance_schedules, service_requests

-- =====================================================
-- ENHANCE EXISTING TABLES WITH MISSING COLUMNS
-- =====================================================

-- Add aquarium-specific columns to existing products table
DO $$
BEGIN
    -- Add missing columns to products table
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'short_description') THEN
        ALTER TABLE public.products ADD COLUMN short_description TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'sale_price') THEN
        ALTER TABLE public.products ADD COLUMN sale_price DECIMAL(10,2);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'weight') THEN
        ALTER TABLE public.products ADD COLUMN weight DECIMAL(8,2);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'dimensions') THEN
        ALTER TABLE public.products ADD COLUMN dimensions TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'care_instructions') THEN
        ALTER TABLE public.products ADD COLUMN care_instructions TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'compatibility') THEN
        ALTER TABLE public.products ADD COLUMN compatibility TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'water_parameters') THEN
        ALTER TABLE public.products ADD COLUMN water_parameters JSONB;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'lighting_requirements') THEN
        ALTER TABLE public.products ADD COLUMN lighting_requirements TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'difficulty_level') THEN
        ALTER TABLE public.products ADD COLUMN difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced'));
    END IF;
END $$;

-- Add missing columns to user_profiles table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'auth_user_id') THEN
        ALTER TABLE public.user_profiles ADD COLUMN auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'date_of_birth') THEN
        ALTER TABLE public.user_profiles ADD COLUMN date_of_birth DATE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'role') THEN
        ALTER TABLE public.user_profiles ADD COLUMN role TEXT DEFAULT 'customer' CHECK (role IN ('customer', 'admin', 'super_admin'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'is_active') THEN
        ALTER TABLE public.user_profiles ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'email_verified') THEN
        ALTER TABLE public.user_profiles ADD COLUMN email_verified BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'marketing_consent') THEN
        ALTER TABLE public.user_profiles ADD COLUMN marketing_consent BOOLEAN DEFAULT false;
    END IF;
END $$;

-- =====================================================
-- CREATE MISSING TABLES
-- =====================================================

-- Product categories (hierarchical categories)
CREATE TABLE IF NOT EXISTS public.product_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    slug TEXT NOT NULL UNIQUE,
    parent_id UUID REFERENCES public.product_categories(id),
    image_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product variants (size, color, material variations)
CREATE TABLE IF NOT EXISTS public.product_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    sku TEXT UNIQUE,
    price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    stock_quantity INTEGER DEFAULT 0,
    variant_options JSONB, -- {size: "Large", color: "Blue", etc}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product images (multiple images per product)
CREATE TABLE IF NOT EXISTS public.product_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shopping carts (persistent carts)
CREATE TABLE IF NOT EXISTS public.shopping_carts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart items (items in shopping carts)
CREATE TABLE IF NOT EXISTS public.cart_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cart_id UUID REFERENCES public.shopping_carts(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES public.product_variants(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wishlist items (user wishlists)
CREATE TABLE IF NOT EXISTS public.wishlist_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Customer addresses (saved shipping/billing addresses)
CREATE TABLE IF NOT EXISTS public.customer_addresses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('shipping', 'billing')),
    first_name TEXT,
    last_name TEXT,
    company TEXT,
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    postal_code TEXT NOT NULL,
    country TEXT DEFAULT 'ZA',
    phone TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blog categories
CREATE TABLE IF NOT EXISTS public.blog_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    slug TEXT NOT NULL UNIQUE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FAQ categories
CREATE TABLE IF NOT EXISTS public.faq_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FAQs (better structured than existing faq_entries)
CREATE TABLE IF NOT EXISTS public.faqs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category_id UUID REFERENCES public.faq_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Custom order requests (bespoke aquarium designs)
CREATE TABLE IF NOT EXISTS public.custom_order_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    budget_range TEXT,
    preferred_timeline TEXT,
    aquarium_dimensions TEXT,
    aquarium_type TEXT, -- freshwater, saltwater, reef, etc.
    special_requirements TEXT,
    reference_images TEXT[], -- Array of image URLs
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'quote_provided', 'approved', 'in_progress', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    estimated_cost DECIMAL(10,2),
    estimated_timeline TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Custom order quotes (pricing for custom requests)
CREATE TABLE IF NOT EXISTS public.custom_order_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    custom_order_request_id UUID REFERENCES public.custom_order_requests(id) ON DELETE CASCADE,
    quote_number TEXT UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    labor_cost DECIMAL(10,2),
    materials_cost DECIMAL(10,2),
    equipment_cost DECIMAL(10,2),
    timeline_weeks INTEGER,
    terms_and_conditions TEXT,
    valid_until DATE,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'accepted', 'rejected', 'expired')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Maintenance schedules (ongoing aquarium maintenance)
CREATE TABLE IF NOT EXISTS public.maintenance_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    aquarium_name TEXT NOT NULL,
    aquarium_type TEXT,
    tank_size TEXT,
    maintenance_frequency TEXT CHECK (maintenance_frequency IN ('weekly', 'bi-weekly', 'monthly', 'quarterly')),
    next_maintenance_date DATE,
    last_maintenance_date DATE,
    special_instructions TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Service requests (maintenance, repairs, consultations)
CREATE TABLE IF NOT EXISTS public.service_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    service_type TEXT NOT NULL CHECK (service_type IN ('maintenance', 'repair', 'consultation', 'installation', 'emergency')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    preferred_date DATE,
    preferred_time TIME,
    address TEXT,
    phone TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'scheduled', 'in_progress', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    technician_notes TEXT,
    customer_rating INTEGER CHECK (customer_rating >= 1 AND customer_rating <= 5),
    customer_feedback TEXT,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Product-related indexes
CREATE INDEX IF NOT EXISTS idx_product_categories_parent_id ON public.product_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_product_categories_slug ON public.product_categories(slug);
CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON public.product_variants(product_id);
CREATE INDEX IF NOT EXISTS idx_product_images_product_id ON public.product_images(product_id);

-- Shopping cart indexes
CREATE INDEX IF NOT EXISTS idx_shopping_carts_user_id ON public.shopping_carts(user_id);
CREATE INDEX IF NOT EXISTS idx_shopping_carts_session_id ON public.shopping_carts(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON public.cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON public.cart_items(product_id);

-- Wishlist indexes
CREATE INDEX IF NOT EXISTS idx_wishlist_items_user_id ON public.wishlist_items(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_product_id ON public.wishlist_items(product_id);

-- Customer addresses indexes
CREATE INDEX IF NOT EXISTS idx_customer_addresses_user_id ON public.customer_addresses(user_id);

-- Blog indexes
CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON public.blog_categories(slug);

-- FAQ indexes
CREATE INDEX IF NOT EXISTS idx_faqs_category_id ON public.faqs(category_id);

-- Custom orders indexes
CREATE INDEX IF NOT EXISTS idx_custom_order_requests_user_id ON public.custom_order_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_order_requests_status ON public.custom_order_requests(status);
CREATE INDEX IF NOT EXISTS idx_custom_order_quotes_request_id ON public.custom_order_quotes(custom_order_request_id);

-- Maintenance indexes
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_user_id ON public.maintenance_schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_next_date ON public.maintenance_schedules(next_maintenance_date);

-- Service requests indexes
CREATE INDEX IF NOT EXISTS idx_service_requests_user_id ON public.service_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_service_requests_status ON public.service_requests(status);
CREATE INDEX IF NOT EXISTS idx_service_requests_scheduled_at ON public.service_requests(scheduled_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on user-specific tables
ALTER TABLE public.shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.custom_order_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.custom_order_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maintenance_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_requests ENABLE ROW LEVEL SECURITY;

-- Shopping carts policies
CREATE POLICY "Users can manage their own cart" ON public.shopping_carts
    FOR ALL USING (user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid()));

-- Cart items policies (through cart ownership)
CREATE POLICY "Users can manage their own cart items" ON public.cart_items
    FOR ALL USING (cart_id IN (
        SELECT id FROM public.shopping_carts 
        WHERE user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid())
    ));

-- Wishlist policies
CREATE POLICY "Users can manage their own wishlist" ON public.wishlist_items
    FOR ALL USING (auth.uid() = user_id);

-- Customer addresses policies
CREATE POLICY "Users can manage their own addresses" ON public.customer_addresses
    FOR ALL USING (user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid()));

-- Custom order requests policies
CREATE POLICY "Users can manage their own custom orders" ON public.custom_order_requests
    FOR ALL USING (user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid()));

-- Custom order quotes policies (users can view quotes for their requests)
CREATE POLICY "Users can view quotes for their requests" ON public.custom_order_quotes
    FOR SELECT USING (custom_order_request_id IN (
        SELECT id FROM public.custom_order_requests 
        WHERE user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid())
    ));

-- Maintenance schedules policies
CREATE POLICY "Users can manage their own maintenance schedules" ON public.maintenance_schedules
    FOR ALL USING (user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid()));

-- Service requests policies
CREATE POLICY "Users can manage their own service requests" ON public.service_requests
    FOR ALL USING (user_id IN (SELECT id FROM public.user_profiles WHERE auth_user_id = auth.uid()));

-- Admin policies (for users with admin role)
CREATE POLICY "Admins can manage all custom orders" ON public.custom_order_requests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

CREATE POLICY "Admins can manage all quotes" ON public.custom_order_quotes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

CREATE POLICY "Admins can manage all service requests" ON public.service_requests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- TRIGGERS FOR UPDATED_AT COLUMNS
-- =====================================================

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to new tables with updated_at columns
DO $$
DECLARE
    table_name TEXT;
    new_tables_with_updated_at TEXT[] := ARRAY[
        'product_categories', 'product_variants', 'shopping_carts', 'cart_items',
        'wishlist_items', 'customer_addresses', 'blog_categories', 'faq_categories',
        'faqs', 'custom_order_requests', 'custom_order_quotes', 'maintenance_schedules',
        'service_requests'
    ];
BEGIN
    FOREACH table_name IN ARRAY new_tables_with_updated_at
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%I_updated_at ON public.%I', table_name, table_name);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at BEFORE UPDATE ON public.%I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', table_name, table_name);
    END LOOP;
END $$;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default product categories
INSERT INTO public.product_categories (name, description, slug, sort_order) VALUES
('Aquarium Equipment', 'Filters, pumps, heaters, and other equipment', 'aquarium-equipment', 1),
('Fish', 'Tropical, marine, and freshwater fish', 'fish', 2),
('Plants', 'Live aquatic plants and decorations', 'plants', 3),
('Decorations', 'Rocks, driftwood, and ornaments', 'decorations', 4),
('Food & Care', 'Fish food, water treatments, and care products', 'food-care', 5),
('Tanks & Stands', 'Aquarium tanks and supporting furniture', 'tanks-stands', 6)
ON CONFLICT (name) DO NOTHING;

-- Insert default blog categories
INSERT INTO public.blog_categories (name, description, slug, sort_order) VALUES
('Aquarium Care', 'Tips and guides for aquarium maintenance', 'aquarium-care', 1),
('Product Reviews', 'Reviews of aquarium products and equipment', 'product-reviews', 2),
('Design Inspiration', 'Aquascape design ideas and inspiration', 'design-inspiration', 3),
('Fish Care', 'Fish health and care guides', 'fish-care', 4),
('Plant Care', 'Aquatic plant care and cultivation', 'plant-care', 5)
ON CONFLICT (name) DO NOTHING;

-- Insert default FAQ categories
INSERT INTO public.faq_categories (name, description, sort_order) VALUES
('General', 'General questions about our services', 1),
('Products', 'Questions about our aquarium products', 2),
('Maintenance', 'Aquarium maintenance and care', 3),
('Custom Orders', 'Custom aquarium design questions', 4),
('Shipping', 'Shipping and delivery information', 5)
ON CONFLICT (name) DO NOTHING;

-- Success message
SELECT 'Missing e-commerce tables added successfully! Database is now complete.' as status;
