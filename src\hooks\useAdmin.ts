import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useError<PERSON><PERSON><PERSON> } from '@/components/ui/error-boundary';
import type { 
  PaginationParams, 
  FilterParams, 
  FormState, 
  ApiResponse,
  PaginatedResponse,
  BaseEntity 
} from '@/types/admin';

// Generic CRUD hook
export const useCRUD = <T extends BaseEntity>(tableName: string) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { handleError } = useErrorHandler();

  const fetchAll = useCallback(async (options?: {
    select?: string;
    filters?: Record<string, any>;
    orderBy?: { column: string; ascending?: boolean };
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from(tableName).select(options?.select || '*');

      if (options?.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            query = query.eq(key, value);
          }
        });
      }

      if (options?.orderBy) {
        query = query.order(options.orderBy.column, { 
          ascending: options.orderBy.ascending ?? true 
        });
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data: result, error } = await query;

      if (error) throw error;
      setData(result || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
      setError(errorMessage);
      handleError(err instanceof Error ? err : new Error(errorMessage), `fetching ${tableName}`);
    } finally {
      setLoading(false);
    }
  }, [tableName, handleError]);

  const create = useCallback(async (item: Omit<T, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .insert(item)
        .select()
        .single();

      if (error) throw error;
      
      setData(prev => [result, ...prev]);
      toast.success('Item created successfully!');
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create item';
      handleError(err instanceof Error ? err : new Error(errorMessage), `creating ${tableName}`);
      throw err;
    }
  }, [tableName, handleError]);

  const update = useCallback(async (id: string, updates: Partial<T>) => {
    try {
      const { data: result, error } = await supabase
        .from(tableName)
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      setData(prev => prev.map(item => item.id === id ? result : item));
      toast.success('Item updated successfully!');
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update item';
      handleError(err instanceof Error ? err : new Error(errorMessage), `updating ${tableName}`);
      throw err;
    }
  }, [tableName, handleError]);

  const remove = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', id);

      if (error) throw error;

      setData(prev => prev.filter(item => item.id !== id));
      toast.success('Item deleted successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete item';
      handleError(err instanceof Error ? err : new Error(errorMessage), `deleting ${tableName}`);
      throw err;
    }
  }, [tableName, handleError]);

  const bulkDelete = useCallback(async (ids: string[]) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .in('id', ids);

      if (error) throw error;

      setData(prev => prev.filter(item => !ids.includes(item.id)));
      toast.success(`${ids.length} items deleted successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete items';
      handleError(err instanceof Error ? err : new Error(errorMessage), `bulk deleting ${tableName}`);
      throw err;
    }
  }, [tableName, handleError]);

  return {
    data,
    loading,
    error,
    fetchAll,
    create,
    update,
    remove,
    bulkDelete,
    refresh: fetchAll
  };
};

// Form management hook
export const useForm = <T>(
  initialData: T,
  validationSchema?: any
) => {
  const [formState, setFormState] = useState<FormState<T>>({
    data: initialData,
    errors: {},
    isSubmitting: false,
    isDirty: false
  });

  const updateField = useCallback((field: keyof T, value: any) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, [field]: value },
      isDirty: true,
      errors: { ...prev.errors, [field as string]: '' }
    }));
  }, []);

  const updateData = useCallback((data: Partial<T>) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, ...data },
      isDirty: true
    }));
  }, []);

  const setErrors = useCallback((errors: Record<string, string>) => {
    setFormState(prev => ({ ...prev, errors }));
  }, []);

  const validate = useCallback(() => {
    if (!validationSchema) return true;

    try {
      validationSchema.parse(formState.data);
      setErrors({});
      return true;
    } catch (error: any) {
      if (error.errors) {
        const validationErrors: Record<string, string> = {};
        error.errors.forEach((err: any) => {
          const field = err.path.join('.');
          validationErrors[field] = err.message;
        });
        setErrors(validationErrors);
      }
      return false;
    }
  }, [formState.data, validationSchema, setErrors]);

  const reset = useCallback((data?: T) => {
    setFormState({
      data: data || initialData,
      errors: {},
      isSubmitting: false,
      isDirty: false
    });
  }, [initialData]);

  const submit = useCallback(async (onSubmit: (data: T) => Promise<void>) => {
    if (!validate()) return;

    setFormState(prev => ({ ...prev, isSubmitting: true }));
    
    try {
      await onSubmit(formState.data);
      setFormState(prev => ({ ...prev, isDirty: false }));
    } catch (error) {
      // Error handling is done by the onSubmit function
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [formState.data, validate]);

  return {
    ...formState,
    updateField,
    updateData,
    setErrors,
    validate,
    reset,
    submit
  };
};

// Pagination hook
export const usePagination = (initialParams: PaginationParams = { page: 1, limit: 10 }) => {
  const [params, setParams] = useState(initialParams);

  const setPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  const setLimit = useCallback((limit: number) => {
    setParams(prev => ({ ...prev, limit, page: 1 }));
  }, []);

  const setSort = useCallback((sortBy: string, sortOrder: 'asc' | 'desc' = 'asc') => {
    setParams(prev => ({ ...prev, sortBy, sortOrder, page: 1 }));
  }, []);

  const reset = useCallback(() => {
    setParams(initialParams);
  }, [initialParams]);

  return {
    params,
    setPage,
    setLimit,
    setSort,
    reset
  };
};

// Search and filter hook
export const useSearch = <T>(
  data: T[],
  searchFields: (keyof T)[],
  initialFilters: FilterParams = {}
) => {
  const [filters, setFilters] = useState(initialFilters);

  const filteredData = useMemo(() => {
    let result = data;

    // Text search
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(item =>
        searchFields.some(field => {
          const value = item[field];
          return value && String(value).toLowerCase().includes(searchTerm);
        })
      );
    }

    // Other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (key !== 'search' && value && value !== 'all') {
        result = result.filter(item => {
          const itemValue = item[key as keyof T];
          return itemValue === value;
        });
      }
    });

    return result;
  }, [data, filters, searchFields]);

  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters]);

  return {
    filteredData,
    filters,
    updateFilter,
    clearFilters
  };
};

// Selection hook for bulk operations
export const useSelection = <T extends { id: string }>(data: T[]) => {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedIds(new Set(data.map(item => item.id)));
  }, [data]);

  const clearSelection = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  const toggleSelectAll = useCallback(() => {
    if (selectedIds.size === data.length) {
      clearSelection();
    } else {
      selectAll();
    }
  }, [selectedIds.size, data.length, clearSelection, selectAll]);

  const selectedItems = useMemo(() => 
    data.filter(item => selectedIds.has(item.id)),
    [data, selectedIds]
  );

  return {
    selectedIds,
    selectedItems,
    toggleSelection,
    selectAll,
    clearSelection,
    toggleSelectAll,
    hasSelection: selectedIds.size > 0,
    isAllSelected: selectedIds.size === data.length && data.length > 0
  };
};

// Local storage hook
export const useLocalStorage = <T>(key: string, initialValue: T) => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue] as const;
};

// Debounce hook
export const useDebounce = <T>(value: T, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};
