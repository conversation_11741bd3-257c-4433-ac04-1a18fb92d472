-- Safe fix for site_settings table data type issues
-- This migration handles the conversion more carefully

-- 1. First, let's see what we're working with and fix it step by step
DO $$
DECLARE
    col_type TEXT;
BEGIN
    -- Get the current data type of the value column
    SELECT data_type INTO col_type
    FROM information_schema.columns 
    WHERE table_name = 'site_settings' 
    AND column_name = 'value'
    AND table_schema = 'public';
    
    -- If it's text, we need to handle the conversion carefully
    IF col_type = 'text' THEN
        -- Create a temporary column to store the converted values
        ALTER TABLE public.site_settings ADD COLUMN IF NOT EXISTS value_temp JSONB;
        
        -- Convert existing data to proper JSON format
        UPDATE public.site_settings SET value_temp = CASE
            -- Handle boolean values
            WHEN value = 'true' THEN 'true'::JSONB
            WHEN value = 'false' THEN 'false'::JSONB
            -- Handle numeric values
            WHEN value ~ '^[0-9]+\.?[0-9]*$' THEN value::JSONB
            -- <PERSON><PERSON> already quoted strings
            WHEN value ~ '^".*"$' THEN value::JSONB
            -- Handle objects and arrays (already valid JSON)
            WHEN value ~ '^\{.*\}$' OR value ~ '^\[.*\]$' THEN value::JSONB
            -- Handle unquoted strings - quote them
            ELSE ('"' || value || '"')::JSONB
        END
        WHERE value IS NOT NULL;
        
        -- Drop the old column and rename the new one
        ALTER TABLE public.site_settings DROP COLUMN value;
        ALTER TABLE public.site_settings RENAME COLUMN value_temp TO value;
        
    END IF;
END $$;

-- 2. Ensure the column has a proper default and constraints
ALTER TABLE public.site_settings ALTER COLUMN value SET DEFAULT NULL;

-- 3. Add RLS policies for site_settings (admin management)
DROP POLICY IF EXISTS "Admins can manage site settings" ON public.site_settings;
CREATE POLICY "Admins can manage site settings" ON public.site_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role IN ('admin', 'super_admin')
    )
  );

-- 4. Add public read policy for site_settings (for homepage content)
DROP POLICY IF EXISTS "Site settings are publicly readable" ON public.site_settings;
CREATE POLICY "Site settings are publicly readable" ON public.site_settings
  FOR SELECT USING (true);

-- 5. Enable RLS on site_settings
ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;

-- 6. Add index for better performance
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON public.site_settings(key);

-- 7. Insert or update default settings with proper JSON format
INSERT INTO public.site_settings (key, value, description) VALUES
  ('site_name', '"Depths of Perception"'::JSONB, 'Website name'),
  ('site_description', '"Custom aquarium decorations by a renowned sculptor"'::JSONB, 'Website description'),
  ('contact_email', '"<EMAIL>"'::JSONB, 'Contact email'),
  ('shipping_enabled', 'true'::JSONB, 'Enable shipping calculations'),
  ('tax_rate', '0.08'::JSONB, 'Default tax rate'),
  ('currency', '"USD"'::JSONB, 'Default currency')
ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  description = EXCLUDED.description,
  updated_at = NOW();

-- 8. Verify the conversion worked
DO $$
DECLARE
    rec RECORD;
    error_count INTEGER := 0;
BEGIN
    -- Check if all values are valid JSONB
    FOR rec IN SELECT key, value FROM public.site_settings LOOP
        BEGIN
            -- Try to access the JSONB value
            PERFORM rec.value::TEXT;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Invalid JSONB in site_settings for key: %', rec.key;
            error_count := error_count + 1;
        END;
    END LOOP;
    
    IF error_count = 0 THEN
        RAISE NOTICE 'Site settings conversion completed successfully!';
    ELSE
        RAISE NOTICE 'Found % invalid JSONB values in site_settings', error_count;
    END IF;
END $$;
