[{"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "instance_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "payload", "data_type": "json", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "ip_address", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "auth_code", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "code_challenge_method", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "code_challenge", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_type", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_access_token", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_refresh_token", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "authentication_method", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "auth_code_issued_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "provider_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "identity_data", "data_type": "jsonb", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "provider", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "last_sign_in_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "email", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "identities", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "auth", "table_name": "instances", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "instances", "column_name": "uuid", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "instances", "column_name": "raw_base_config", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "instances", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "instances", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "session_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "authentication_method", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "factor_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "verified_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "ip_address", "data_type": "inet", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "otp_code", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "web_authn_session_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "friendly_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "factor_type", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "secret", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "last_challenged_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "web_authn_credential", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "web_authn_aaguid", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "token_type", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "token_hash", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "relates_to", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "updated_at", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "instance_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "id", "data_type": "bigint", "is_nullable": "NO", "column_default": "nextval('auth.refresh_tokens_id_seq'::regclass)"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "token", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "user_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "revoked", "data_type": "boolean", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "parent", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "session_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "sso_provider_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "entity_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "metadata_xml", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "metadata_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "attribute_mapping", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "name_id_format", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "sso_provider_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "request_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "for_email", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "redirect_to", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "flow_state_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "schema_migrations", "column_name": "version", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "factor_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "aal", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "not_after", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "refreshed_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "user_agent", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "ip", "data_type": "inet", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sessions", "column_name": "tag", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "sso_provider_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "domain", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "resource_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "instance_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "aud", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "role", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "encrypted_password", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email_confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "invited_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmation_token", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmation_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "recovery_token", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "recovery_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_token_new", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "last_sign_in_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "raw_app_meta_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "raw_user_meta_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "is_super_admin", "data_type": "boolean", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": "NULL::character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change", "data_type": "text", "is_nullable": "YES", "column_default": "''::character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change_token", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_token_current", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_confirm_status", "data_type": "smallint", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "auth", "table_name": "users", "column_name": "banned_until", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "reauthentication_token", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "reauthentication_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "is_sso_user", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_schema": "auth", "table_name": "users", "column_name": "deleted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "auth", "table_name": "users", "column_name": "is_anonymous", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "userid", "data_type": "oid", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "dbid", "data_type": "oid", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "toplevel", "data_type": "boolean", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "queryid", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "query", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "plans", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "total_plan_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "min_plan_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "max_plan_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "mean_plan_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stddev_plan_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "calls", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "total_exec_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "min_exec_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "max_exec_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "mean_exec_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stddev_exec_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "rows", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_hit", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_read", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_dirtied", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_written", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_hit", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_read", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_dirtied", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_written", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blks_read", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blks_written", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blk_read_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blk_write_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blk_read_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blk_write_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blk_read_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blk_write_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_records", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_fpi", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_bytes", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_functions", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_generation_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_inlining_count", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_inlining_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_optimization_count", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_optimization_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_emission_count", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_emission_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_deform_count", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_deform_time", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stats_since", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "minmax_stats_since", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements_info", "column_name": "dealloc", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "extensions", "table_name": "pg_stat_statements_info", "column_name": "stats_reset", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "bio", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "full_biography", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "portrait_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "hero_image_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "website_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "social_instagram", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "social_facebook", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "years_experience", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "total_pieces_created", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "artist_profile", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "title", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "slug", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "content", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "excerpt", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "featured_image_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "author_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "status", "data_type": "text", "is_nullable": "YES", "column_default": "'draft'::text"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "published_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "meta_title", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "meta_description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "tags", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "blog_posts", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "subject", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "message", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "project_type", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "budget_range", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "is_read", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "public", "table_name": "contact_submissions", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "question", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "answer", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "category", "data_type": "text", "is_nullable": "YES", "column_default": "'general'::text"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "is_active", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "faq_entries", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "status", "data_type": "text", "is_nullable": "YES", "column_default": "'active'::text"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "subscribed_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "unsubscribed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "tags", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": "'{}'::jsonb"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "newsletter_subscribers", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "order_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "type", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "first_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "last_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "company", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "address_line_1", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "address_line_2", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "city", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "state", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "postal_code", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "country", "data_type": "text", "is_nullable": "YES", "column_default": "'US'::text"}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "order_addresses", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "order_items", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "order_items", "column_name": "order_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_items", "column_name": "product_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_items", "column_name": "product_name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "order_items", "column_name": "product_price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "order_items", "column_name": "quantity", "data_type": "integer", "is_nullable": "NO", "column_default": "1"}, {"table_schema": "public", "table_name": "order_items", "column_name": "total_price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "order_items", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "order_tracking", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "order_tracking", "column_name": "order_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_tracking", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "order_tracking", "column_name": "notes", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "order_tracking", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "orders", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "orders", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "order_number", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'pending'::order_status"}, {"table_schema": "public", "table_name": "orders", "column_name": "payment_status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'pending'::payment_status"}, {"table_schema": "public", "table_name": "orders", "column_name": "subtotal", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "tax_amount", "data_type": "numeric", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "orders", "column_name": "shipping_amount", "data_type": "numeric", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "orders", "column_name": "total_amount", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "shipping_address", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "billing_address", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "notes", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "orders", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "orders", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "title", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "image_url", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "category", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "materials", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "dimensions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "year_created", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "is_available", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "portfolio_items", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "products", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "products", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "image_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "category", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "material", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "size", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "dimensions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "weight", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "care_instructions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "products", "column_name": "in_stock", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_schema": "public", "table_name": "products", "column_name": "stock_quantity", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "products", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "public", "table_name": "products", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_schema": "public", "table_name": "products", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "products", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "products", "column_name": "sku", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "site_settings", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "public", "table_name": "site_settings", "column_name": "key", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "site_settings", "column_name": "value", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "site_settings", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "site_settings", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "site_settings", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "first_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "last_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "role", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'customer'::user_role"}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "avatar_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "public", "table_name": "user_profiles", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "topic", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "messages", "column_name": "extension", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "messages", "column_name": "payload", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "realtime", "table_name": "messages", "column_name": "event", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "realtime", "table_name": "messages", "column_name": "private", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "updated_at", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "inserted_at", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "realtime", "table_name": "schema_migrations", "column_name": "version", "data_type": "bigint", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "schema_migrations", "column_name": "inserted_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "id", "data_type": "bigint", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "subscription_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "entity", "data_type": "regclass", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "filters", "data_type": "ARRAY", "is_nullable": "NO", "column_default": "'{}'::realtime.user_defined_filter[]"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "claims", "data_type": "jsonb", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "claims_role", "data_type": "regrole", "is_nullable": "NO", "column_default": null}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "buckets", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "buckets", "column_name": "owner", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "buckets", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "public", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "avif_autodetection", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "file_size_limit", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "buckets", "column_name": "allowed_mime_types", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "buckets", "column_name": "owner_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "migrations", "column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "migrations", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "migrations", "column_name": "hash", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "migrations", "column_name": "executed_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"table_schema": "storage", "table_name": "objects", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "storage", "table_name": "objects", "column_name": "bucket_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "owner", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "storage", "table_name": "objects", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "storage", "table_name": "objects", "column_name": "last_accessed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_schema": "storage", "table_name": "objects", "column_name": "metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "path_tokens", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "version", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "owner_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "objects", "column_name": "user_metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "in_progress_size", "data_type": "bigint", "is_nullable": "NO", "column_default": "0"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "upload_signature", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "bucket_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "key", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "version", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "owner_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "user_metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "upload_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "size", "data_type": "bigint", "is_nullable": "NO", "column_default": "0"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "part_number", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "bucket_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "key", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "etag", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "owner_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "version", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"table_schema": "supabase_migrations", "table_name": "schema_migrations", "column_name": "version", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "supabase_migrations", "table_name": "schema_migrations", "column_name": "statements", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_schema": "supabase_migrations", "table_name": "schema_migrations", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "supabase_migrations", "table_name": "schema_migrations", "column_name": "created_by", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "supabase_migrations", "table_name": "schema_migrations", "column_name": "idempotency_key", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "secret", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "decrypted_secret", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "key_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "nonce", "data_type": "bytea", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "secrets", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "secrets", "column_name": "description", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "secret", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_schema": "vault", "table_name": "secrets", "column_name": "key_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_schema": "vault", "table_name": "secrets", "column_name": "nonce", "data_type": "bytea", "is_nullable": "YES", "column_default": "vault._crypto_aead_det_noncegen()"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}]