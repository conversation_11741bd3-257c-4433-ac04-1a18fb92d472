# Image Upload System Testing Checklist

## ✅ Implementation Status

### Components Created/Updated:
- [x] `ImageUpload.tsx` - Reusable drag-and-drop component with compression
- [x] `AdminProducts.tsx` - Updated to use ImageUpload
- [x] `AdminPortfolio.tsx` - Updated to use ImageUpload  
- [x] `AdminHomepage.tsx` - Updated to use ImageUpload
- [x] `AdminArtist.tsx` - New component with image upload
- [x] `DepthAdmin.tsx` - Added Artist tab and routing

### Database Setup:
- [x] Storage buckets migration created
- [x] RLS policies defined
- [x] Helper functions for file management

## 🧪 Quick Testing Steps

### 1. Start the Application
```bash
npm run dev
```

### 2. Access Admin Dashboard
1. Navigate to `http://localhost:8080/depthadmin`
2. Sign in with admin credentials
3. Verify all tabs are visible including new "Artist" tab

### 3. Test Each Admin Section

#### Products Section:
1. Go to Admin → Products → Add Product
2. Verify ImageUpload component appears instead of text input
3. Test drag-and-drop or click to upload
4. Verify image preview and form submission

#### Portfolio Section:
1. Go to Admin → Portfolio → Add Portfolio Item
2. Test image upload with landscape aspect ratio
3. Verify image saves correctly

#### Homepage Section:
1. Go to Admin → Homepage
2. Test both hero and about image uploads
3. Verify different aspect ratios work

#### Artist Section (NEW):
1. Go to Admin → Artist
2. Test portrait image upload (square)
3. Test hero image upload (landscape)
4. Fill out artist information and save

### 4. Verify Public Display
1. Navigate to homepage (`http://localhost:8080/`)
2. Check if uploaded images display correctly
3. Verify artist information appears if configured

## 🔍 What to Look For

### Success Indicators:
- ✅ No text input fields for image URLs
- ✅ Drag-and-drop upload areas visible
- ✅ Progress bars during upload
- ✅ Image previews after upload
- ✅ Success/error messages
- ✅ Images display on public pages

### Potential Issues:
- ❌ Storage bucket not configured
- ❌ RLS policies not applied
- ❌ Authentication issues
- ❌ File size/type validation errors
- ❌ Images not displaying publicly

## 🚨 Before Production

### Required Setup:
1. Run storage migration:
   ```sql
   -- Execute: supabase/migrations/20250719070000-setup-storage-buckets.sql
   ```

2. Verify Supabase Storage is enabled in dashboard

3. Check all buckets exist:
   - product-images
   - portfolio-images  
   - artist-images
   - homepage-images

4. Test with real image files of various sizes and formats

### Security Checklist:
- [x] File type validation (JPEG, PNG, WebP, GIF only)
- [x] File size limits (10MB max)
- [x] Authenticated upload only
- [x] Public read access for display
- [x] Unique filename generation

## 📝 Test Results Log

**Date**: ___________
**Tester**: ___________

| Component | Upload Works | Preview Works | Save Works | Public Display | Notes |
|-----------|--------------|---------------|------------|----------------|-------|
| Products  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No   |       |
| Portfolio | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No   |       |
| Homepage  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No   |       |
| Artist    | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No  | ⬜ Yes ⬜ No | ⬜ Yes ⬜ No   |       |

**Overall Status**: ⬜ PASS ⬜ FAIL

**Issues Found**:
- 
- 
- 

**Next Steps**:
- 
- 
- 
