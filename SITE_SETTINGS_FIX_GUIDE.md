# Site Settings Data Type Fix Guide

## 🚨 **The Problem**
The error you encountered:
```
ERROR: 22P02: invalid input syntax for type json
DETAIL: Token "Depths" is invalid.
```

This happens because the `site_settings` table has unquoted string values like `Depths of Perception` that can't be converted to JSONB format.

## 🔧 **Quick Fix (Immediate Solution)**

### Option 1: Manual SQL Fix (Recommended)
Run this in your Supabase SQL editor:

```sql
-- Fix the data by properly quoting unquoted strings
UPDATE site_settings 
SET value = '"' || value || '"'
WHERE value IS NOT NULL 
  AND value != 'true' 
  AND value != 'false'
  AND value !~ '^[0-9]+\.?[0-9]*$'  -- not a number
  AND value !~ '^".*"$'             -- not already quoted
  AND value !~ '^\{.*\}$'           -- not an object
  AND value !~ '^\[.*\]$';          -- not an array

-- Verify the fix worked
SELECT key, value FROM site_settings;
```

### Option 2: Use the Safe Migration
Instead of the original migration, run:
```bash
# Apply the safer migration
supabase db push

# Or manually execute: supabase/migrations/20250719080001-fix-site-settings-safely.sql
```

## 🎯 **What the Fix Does**

### Before Fix:
```
key: site_name, value: Depths of Perception  ❌ (unquoted string)
key: shipping_enabled, value: true           ✅ (valid JSON)
key: tax_rate, value: 0.08                   ✅ (valid JSON)
```

### After Fix:
```
key: site_name, value: "Depths of Perception"  ✅ (quoted string)
key: shipping_enabled, value: true             ✅ (valid JSON)
key: tax_rate, value: 0.08                     ✅ (valid JSON)
```

## 🧪 **Test the Fix**

### 1. Verify Data Format
```sql
-- Check all values are valid JSON
SELECT key, value, value::JSONB as parsed_value FROM site_settings;
```

### 2. Test Admin Homepage Save
1. Go to Admin → Homepage
2. Change the hero title
3. Click "Save Changes"
4. Should see: ✅ "Homepage settings saved successfully!"

### 3. Test Admin Settings Save
1. Go to Admin → Settings  
2. Change any setting
3. Click "Save Settings"
4. Should see: ✅ "Settings saved successfully!"

## 🔍 **Understanding the Issue**

### Why This Happened:
1. **Original Migration**: Created `site_settings` with `JSONB` column
2. **Data Insertion**: Some values were inserted as unquoted strings
3. **Database Reality**: Column ended up as `text` type in some cases
4. **Conversion Attempt**: Failed because unquoted strings aren't valid JSON

### The Solution:
1. **Fix Existing Data**: Quote unquoted strings to make them valid JSON
2. **Update Components**: Handle both text and JSONB values gracefully
3. **Ensure Consistency**: Always store values as proper JSON strings

## 📋 **Files Updated**

### Components Fixed:
- ✅ `AdminHomepage.tsx` - Now handles both text and JSONB values
- ✅ `AdminSettings.tsx` - Enhanced JSON parsing with fallback
- ✅ `AdminProducts.tsx` - Added proper validation
- ✅ `AdminPortfolio.tsx` - Added proper validation  
- ✅ `AdminArtist.tsx` - Added proper validation

### Migrations Created:
- ✅ `20250719080000-fix-admin-save-issues.sql` - Original (has the JSONB conversion issue)
- ✅ `20250719080001-fix-site-settings-safely.sql` - Safer approach
- ✅ `fix-site-settings-data.sql` - Quick manual fix

## ✅ **Success Criteria**

After applying the fix, you should be able to:

1. ✅ **Save Homepage Settings** without errors
2. ✅ **Save Admin Settings** without errors  
3. ✅ **Save Products** with proper validation
4. ✅ **Save Portfolio Items** with proper validation
5. ✅ **Save Artist Profile** with proper validation
6. ✅ **See content** on public pages after saving

## 🚀 **Next Steps**

### 1. Apply the Quick Fix
```sql
-- Run this in Supabase SQL editor
UPDATE site_settings 
SET value = '"' || value || '"'
WHERE value IS NOT NULL 
  AND value != 'true' 
  AND value != 'false'
  AND value !~ '^[0-9]+\.?[0-9]*$'
  AND value !~ '^".*"$'
  AND value !~ '^\{.*\}$'
  AND value !~ '^\[.*\]$';
```

### 2. Test All Admin Sections
- Products ✅
- Portfolio ✅  
- Homepage ✅
- Artist ✅
- Settings ✅

### 3. Verify Public Pages
- Homepage shows updated content ✅
- Products display correctly ✅
- Portfolio displays correctly ✅

## 🆘 **If You Still Have Issues**

### Check Current Data:
```sql
SELECT key, value, pg_typeof(value) FROM site_settings;
```

### Test JSON Conversion:
```sql
SELECT key, value::JSONB FROM site_settings;
```

### Reset Site Settings (Nuclear Option):
```sql
-- Only if everything else fails
DELETE FROM site_settings;
INSERT INTO site_settings (key, value, description) VALUES
  ('site_name', '"Depths of Perception"', 'Website name'),
  ('site_description', '"Custom aquarium decorations by a renowned sculptor"', 'Website description'),
  ('contact_email', '"<EMAIL>"', 'Contact email'),
  ('shipping_enabled', 'true', 'Enable shipping calculations'),
  ('tax_rate', '0.08', 'Default tax rate'),
  ('currency', '"USD"', 'Default currency');
```

The admin save functionality should work perfectly after this fix! 🎉
