import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const AdminTestAuth = () => {
  const { user, signIn, signUp, signOut, isAdmin } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSignUp = async () => {
    setLoading(true);
    setMessage('');
    try {
      const result = await signUp(email, password);
      if (result.error) {
        setMessage(`Sign up error: ${result.error}`);
      } else {
        setMessage('Sign up successful! Check your email for verification.');
      }
    } catch (error) {
      setMessage(`Unexpected error: ${error}`);
    }
    setLoading(false);
  };

  const handleSignIn = async () => {
    setLoading(true);
    setMessage('');
    try {
      const result = await signIn(email, password);
      if (result.error) {
        setMessage(`Sign in error: ${result.error}`);
      } else {
        setMessage('Sign in successful!');
      }
    } catch (error) {
      setMessage(`Unexpected error: ${error}`);
    }
    setLoading(false);
  };

  const handleSignOut = async () => {
    await signOut();
    setMessage('Signed out successfully');
  };

  return (
    <Card className="fixed top-4 right-4 w-80 z-50">
      <CardHeader>
        <CardTitle className="text-sm">Admin Auth Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {user ? (
          <div className="space-y-2">
            <p className="text-xs">
              <strong>User:</strong> {user.email}
            </p>
            <p className="text-xs">
              <strong>Admin:</strong> {isAdmin ? 'Yes' : 'No'}
            </p>
            <Button onClick={handleSignOut} size="sm" variant="outline">
              Sign Out
            </Button>
            {isAdmin && (
              <Button 
                onClick={() => window.location.href = '/depthadmin'} 
                size="sm"
                className="w-full"
              >
                Go to Admin Panel
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <Input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="text-xs"
            />
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="text-xs"
            />
            <div className="flex gap-2">
              <Button 
                onClick={handleSignIn} 
                disabled={loading}
                size="sm"
                className="flex-1"
              >
                Sign In
              </Button>
              <Button 
                onClick={handleSignUp} 
                disabled={loading}
                size="sm"
                variant="outline"
                className="flex-1"
              >
                Sign Up
              </Button>
            </div>
          </div>
        )}
        {message && (
          <p className="text-xs text-muted-foreground">{message}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default AdminTestAuth;
