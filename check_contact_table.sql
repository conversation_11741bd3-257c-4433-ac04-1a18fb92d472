-- Check the contact_submissions table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'contact_submissions' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'contact_submissions';

-- Test insert permissions
SELECT has_table_privilege('public.contact_submissions', 'INSERT') as can_insert;
