import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Home, 
  Building, 
  Star,
  Save,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface CustomerAddress {
  id?: string;
  user_id: string;
  type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  label?: string;
  first_name: string;
  last_name: string;
  company?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
}

const emptyAddress: CustomerAddress = {
  user_id: '',
  type: 'both',
  is_default: false,
  label: '',
  first_name: '',
  last_name: '',
  company: '',
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  postal_code: '',
  country: 'US',
  phone: ''
};

export const CustomerAddresses = () => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState<CustomerAddress[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddressDialog, setShowAddressDialog] = useState(false);
  const [editingAddress, setEditingAddress] = useState<CustomerAddress | null>(null);
  const [formData, setFormData] = useState<CustomerAddress>(emptyAddress);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      fetchAddresses();
    }
  }, [user]);

  const fetchAddresses = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('customer_addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAddresses(data || []);
    } catch (error) {
      console.error('Error fetching addresses:', error);
      toast.error('Failed to fetch addresses');
    } finally {
      setLoading(false);
    }
  };

  const openAddressDialog = (address?: CustomerAddress) => {
    if (address) {
      setEditingAddress(address);
      setFormData(address);
    } else {
      setEditingAddress(null);
      setFormData({ ...emptyAddress, user_id: user?.id || '' });
    }
    setShowAddressDialog(true);
  };

  const closeAddressDialog = () => {
    setShowAddressDialog(false);
    setEditingAddress(null);
    setFormData(emptyAddress);
  };

  const saveAddress = async () => {
    if (!user) return;

    try {
      setSaving(true);

      // Validate required fields
      if (!formData.first_name.trim() || !formData.last_name.trim() || 
          !formData.address_line_1.trim() || !formData.city.trim() || 
          !formData.state.trim() || !formData.postal_code.trim()) {
        toast.error('Please fill in all required fields');
        return;
      }

      // If setting as default, remove default from other addresses
      if (formData.is_default) {
        await supabase
          .from('customer_addresses')
          .update({ is_default: false })
          .eq('user_id', user.id);
      }

      if (editingAddress) {
        // Update existing address
        const { error } = await supabase
          .from('customer_addresses')
          .update(formData)
          .eq('id', editingAddress.id);

        if (error) throw error;
        toast.success('Address updated successfully!');
      } else {
        // Create new address
        const { error } = await supabase
          .from('customer_addresses')
          .insert(formData);

        if (error) throw error;
        toast.success('Address added successfully!');
      }

      closeAddressDialog();
      fetchAddresses();
    } catch (error: any) {
      console.error('Error saving address:', error);
      toast.error(`Failed to save address: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const deleteAddress = async (addressId: string) => {
    if (!confirm('Are you sure you want to delete this address?')) return;

    try {
      const { error } = await supabase
        .from('customer_addresses')
        .delete()
        .eq('id', addressId);

      if (error) throw error;
      toast.success('Address deleted successfully!');
      fetchAddresses();
    } catch (error: any) {
      console.error('Error deleting address:', error);
      toast.error(`Failed to delete address: ${error.message}`);
    }
  };

  const setAsDefault = async (addressId: string) => {
    if (!user) return;

    try {
      // Remove default from all addresses
      await supabase
        .from('customer_addresses')
        .update({ is_default: false })
        .eq('user_id', user.id);

      // Set the selected address as default
      const { error } = await supabase
        .from('customer_addresses')
        .update({ is_default: true })
        .eq('id', addressId);

      if (error) throw error;
      toast.success('Default address updated!');
      fetchAddresses();
    } catch (error: any) {
      console.error('Error setting default address:', error);
      toast.error(`Failed to set default address: ${error.message}`);
    }
  };

  const getAddressTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      shipping: 'bg-blue-100 text-blue-800',
      billing: 'bg-green-100 text-green-800',
      both: 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const formatAddress = (address: CustomerAddress) => {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      `${address.city}, ${address.state} ${address.postal_code}`,
      address.country !== 'US' ? address.country : ''
    ].filter(Boolean);
    
    return parts.join('\n');
  };

  if (loading) {
    return <div className="text-center py-8">Loading your addresses...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Addresses</h2>
          <p className="text-muted-foreground">
            Manage your shipping and billing addresses
          </p>
        </div>
        <Dialog open={showAddressDialog} onOpenChange={setShowAddressDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => openAddressDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingAddress ? 'Edit Address' : 'Add New Address'}
              </DialogTitle>
            </DialogHeader>
            <AddressForm 
              address={formData}
              setAddress={setFormData}
              onSave={saveAddress}
              onCancel={closeAddressDialog}
              saving={saving}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Addresses Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {addresses.length > 0 ? (
          addresses.map((address) => (
            <Card key={address.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg">
                      {address.label || `${address.first_name} ${address.last_name}`}
                    </CardTitle>
                    {address.is_default && (
                      <Badge variant="default" className="text-xs">
                        <Star className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </div>
                  <Badge className={getAddressTypeColor(address.type)}>
                    {address.type}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm">
                  <p className="font-medium">
                    {address.first_name} {address.last_name}
                  </p>
                  {address.company && (
                    <p className="text-muted-foreground">{address.company}</p>
                  )}
                  <div className="mt-2 whitespace-pre-line text-muted-foreground">
                    {formatAddress(address)}
                  </div>
                  {address.phone && (
                    <p className="mt-2 text-muted-foreground">{address.phone}</p>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openAddressDialog(address)}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  {!address.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAsDefault(address.id!)}
                    >
                      <Star className="h-3 w-3 mr-1" />
                      Set Default
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteAddress(address.id!)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full">
            <Card>
              <CardContent className="text-center py-12">
                <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No addresses yet</h3>
                <p className="text-muted-foreground mb-4">
                  Add your first address to get started with orders and deliveries.
                </p>
                <Button onClick={() => openAddressDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Address
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

// Address Form Component
const AddressForm = ({ 
  address, 
  setAddress, 
  onSave, 
  onCancel, 
  saving 
}: {
  address: CustomerAddress;
  setAddress: (address: CustomerAddress) => void;
  onSave: () => void;
  onCancel: () => void;
  saving: boolean;
}) => {
  const US_STATES = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="label">Address Label (Optional)</Label>
          <Input
            id="label"
            value={address.label || ''}
            onChange={(e) => setAddress({ ...address, label: e.target.value })}
            placeholder="e.g., Home, Office, Studio"
          />
        </div>

        <div>
          <Label htmlFor="type">Address Type</Label>
          <Select
            value={address.type}
            onValueChange={(value: 'shipping' | 'billing' | 'both') => 
              setAddress({ ...address, type: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="both">Shipping & Billing</SelectItem>
              <SelectItem value="shipping">Shipping Only</SelectItem>
              <SelectItem value="billing">Billing Only</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="first_name">First Name *</Label>
            <Input
              id="first_name"
              value={address.first_name}
              onChange={(e) => setAddress({ ...address, first_name: e.target.value })}
              placeholder="Enter first name"
              required
            />
          </div>
          <div>
            <Label htmlFor="last_name">Last Name *</Label>
            <Input
              id="last_name"
              value={address.last_name}
              onChange={(e) => setAddress({ ...address, last_name: e.target.value })}
              placeholder="Enter last name"
              required
            />
          </div>
        </div>

        <div>
          <Label htmlFor="company">Company (Optional)</Label>
          <Input
            id="company"
            value={address.company || ''}
            onChange={(e) => setAddress({ ...address, company: e.target.value })}
            placeholder="Enter company name"
          />
        </div>

        <div>
          <Label htmlFor="address_line_1">Address Line 1 *</Label>
          <Input
            id="address_line_1"
            value={address.address_line_1}
            onChange={(e) => setAddress({ ...address, address_line_1: e.target.value })}
            placeholder="Enter street address"
            required
          />
        </div>

        <div>
          <Label htmlFor="address_line_2">Address Line 2 (Optional)</Label>
          <Input
            id="address_line_2"
            value={address.address_line_2 || ''}
            onChange={(e) => setAddress({ ...address, address_line_2: e.target.value })}
            placeholder="Apartment, suite, unit, building, floor, etc."
          />
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div>
            <Label htmlFor="city">City *</Label>
            <Input
              id="city"
              value={address.city}
              onChange={(e) => setAddress({ ...address, city: e.target.value })}
              placeholder="Enter city"
              required
            />
          </div>
          <div>
            <Label htmlFor="state">State *</Label>
            <Select
              value={address.state}
              onValueChange={(value) => setAddress({ ...address, state: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {US_STATES.map(state => (
                  <SelectItem key={state} value={state}>{state}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="postal_code">ZIP Code *</Label>
            <Input
              id="postal_code"
              value={address.postal_code}
              onChange={(e) => setAddress({ ...address, postal_code: e.target.value })}
              placeholder="Enter ZIP code"
              required
            />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="country">Country</Label>
            <Select
              value={address.country}
              onValueChange={(value) => setAddress({ ...address, country: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="US">United States</SelectItem>
                <SelectItem value="CA">Canada</SelectItem>
                <SelectItem value="MX">Mexico</SelectItem>
                <SelectItem value="GB">United Kingdom</SelectItem>
                <SelectItem value="AU">Australia</SelectItem>
                <SelectItem value="DE">Germany</SelectItem>
                <SelectItem value="FR">France</SelectItem>
                <SelectItem value="IT">Italy</SelectItem>
                <SelectItem value="ES">Spain</SelectItem>
                <SelectItem value="JP">Japan</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="phone">Phone Number (Optional)</Label>
            <Input
              id="phone"
              type="tel"
              value={address.phone || ''}
              onChange={(e) => setAddress({ ...address, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="is_default"
            checked={address.is_default}
            onChange={(e) => setAddress({ ...address, is_default: e.target.checked })}
            className="rounded"
          />
          <Label htmlFor="is_default" className="text-sm">
            Set as default address
          </Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={onSave} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Address'}
        </Button>
      </div>
    </div>
  );
};
