import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { CustomerDashboard as CustomerDashboardComponent } from '@/components/customer/CustomerDashboard';
import { AdminErrorBoundary } from '@/components/ui/error-boundary';

export default function CustomerDashboard() {
  const { user, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Only allow customers to access this dashboard
  // Admins should use the admin dashboard
  if (user.user_metadata?.role === 'admin' || user.user_metadata?.role === 'super_admin') {
    return <Navigate to="/admin" replace />;
  }

  return (
    <AdminErrorBoundary>
      <CustomerDashboardComponent />
    </AdminErrorBoundary>
  );
}
