-- EMERGENCY FIX (CORRECTED) for 500 errors
-- Run this immediately to restore functionality

-- 1. DISABLE RLS temporarily to restore access
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_profile DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.site_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop all problematic policies
DROP POLICY IF EXISTS "Admins can manage products" ON public.products;
DROP POLICY IF EXISTS "Ad<PERSON> can manage portfolio items" ON public.portfolio_items;
DROP POLICY IF EXISTS "Ad<PERSON> can manage artist profile" ON public.artist_profile;
DROP POLICY IF EXISTS "Ad<PERSON> can manage site settings" ON public.site_settings;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.user_profiles;

-- 3. Check what data type we're working with
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'site_settings' AND column_name = 'value';

-- 4. Fix site_settings data based on actual data type
DO $$
DECLARE
    col_type TEXT;
BEGIN
    -- Get the current data type
    SELECT data_type INTO col_type
    FROM information_schema.columns 
    WHERE table_name = 'site_settings' 
    AND column_name = 'value'
    AND table_schema = 'public';
    
    RAISE NOTICE 'site_settings.value column type: %', col_type;
    
    -- If it's text, fix the data
    IF col_type = 'text' THEN
        UPDATE public.site_settings 
        SET value = '"' || value || '"'
        WHERE value IS NOT NULL 
          AND value != 'true' 
          AND value != 'false'
          AND value !~ '^[0-9]+\.?[0-9]*$'  -- not a number
          AND value !~ '^".*"$'             -- not already quoted
          AND value !~ '^\{.*\}$'           -- not an object
          AND value !~ '^\[.*\]$';          -- not an array
        
        RAISE NOTICE 'Fixed text values in site_settings';
    
    -- If it's already JSONB, check if values are valid
    ELSIF col_type = 'jsonb' THEN
        -- Just verify the data is accessible
        PERFORM key, value FROM public.site_settings LIMIT 1;
        RAISE NOTICE 'site_settings already has JSONB values';
    END IF;
END $$;

-- 5. Create simple, working RLS policies that allow everything
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all for products" ON public.products FOR ALL USING (true);

ALTER TABLE public.portfolio_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all for portfolio" ON public.portfolio_items FOR ALL USING (true);

ALTER TABLE public.artist_profile ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all for artist" ON public.artist_profile FOR ALL USING (true);

ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all for settings" ON public.site_settings FOR ALL USING (true);

ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all for user profiles" ON public.user_profiles FOR ALL USING (true);

-- 6. Verify everything works by counting records
SELECT 'Products count: ' || COUNT(*) as result FROM public.products
UNION ALL
SELECT 'Portfolio count: ' || COUNT(*) FROM public.portfolio_items
UNION ALL
SELECT 'Artist profiles: ' || COUNT(*) FROM public.artist_profile
UNION ALL
SELECT 'Site settings: ' || COUNT(*) FROM public.site_settings
UNION ALL
SELECT 'User profiles: ' || COUNT(*) FROM public.user_profiles;

-- 7. Test a simple site_settings query
SELECT key, value FROM public.site_settings LIMIT 3;