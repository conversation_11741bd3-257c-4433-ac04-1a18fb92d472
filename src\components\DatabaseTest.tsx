import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const DatabaseTest = () => {
  const [testResult, setTestResult] = useState<string>('Testing...');
  const [userProfiles, setUserProfiles] = useState<any[]>([]);

  useEffect(() => {
    const testDatabase = async () => {
      try {
        // Test basic connection
        const { data: artistData, error: artistError } = await supabase
          .from('artist_profile')
          .select('*')
          .limit(1);

        if (artistError) {
          setTestResult(`Artist profile error: ${artistError.message} (Code: ${artistError.code})`);
          return;
        }

        // Test portfolio items instead of user_profiles to avoid RLS issues
        const { data: portfolioData, error: portfolioError } = await supabase
          .from('portfolio_items')
          .select('id, title')
          .limit(5);

        if (portfolioError) {
          setTestResult(`Portfolio error: ${portfolioError.message} (Code: ${portfolioError.code})`);
          console.error('Full error object:', portfolioError);
          return;
        }

        // Try user_profiles with better error handling
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('id, email, role')
          .limit(5);

        if (profileError) {
          setTestResult(`Database OK, but user_profiles RLS issue: ${profileError.message}`);
          console.error('User profiles error:', profileError);
          setUserProfiles([]);
        } else {
          setUserProfiles(profileData || []);
          setTestResult(`Database connection successful! Found ${profileData?.length || 0} profiles`);
        }
      } catch (error) {
        setTestResult(`Unexpected error: ${error}`);
        console.error('Unexpected error:', error);
      }
    };

    testDatabase();
  }, []);

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm z-50">
      <h3 className="font-bold mb-2">Database Test</h3>
      <p className="text-sm mb-2">{testResult}</p>
      {userProfiles.length > 0 && (
        <div>
          <p className="text-xs font-semibold">User Profiles ({userProfiles.length}):</p>
          {userProfiles.map((profile, index) => (
            <div key={index} className="text-xs">
              {profile.email} - {profile.role}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DatabaseTest;
