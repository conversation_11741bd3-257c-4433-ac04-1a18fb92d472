import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  Plus, 
  Eye, 
  Calendar, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Search,
  Filter,
  Image as ImageIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface CustomOrderRequest {
  id: string;
  title: string;
  description: string;
  budget_range?: string;
  preferred_timeline?: string;
  aquarium_dimensions?: string;
  aquarium_type?: string;
  special_requirements?: string;
  reference_images?: string[];
  status: string;
  priority: string;
  estimated_cost?: number;
  estimated_timeline?: string;
  admin_notes?: string;
  customer_notes?: string;
  quote_expires_at?: string;
  created_at: string;
  updated_at: string;
}

export const CustomOrderRequests = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState<CustomOrderRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<CustomOrderRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewRequestDialog, setShowNewRequestDialog] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<CustomOrderRequest | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // New request form state
  const [newRequest, setNewRequest] = useState({
    title: '',
    description: '',
    budget_range: '',
    preferred_timeline: '',
    aquarium_dimensions: '',
    aquarium_type: '',
    special_requirements: '',
    reference_images: [] as string[]
  });

  useEffect(() => {
    if (user) {
      fetchRequests();
    }
  }, [user]);

  useEffect(() => {
    filterRequests();
  }, [requests, searchTerm, statusFilter]);

  const fetchRequests = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('custom_order_requests')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setRequests(data || []);
    } catch (error) {
      console.error('Error fetching custom requests:', error);
      toast.error('Failed to fetch custom requests');
    } finally {
      setLoading(false);
    }
  };

  const filterRequests = () => {
    let filtered = requests;

    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    setFilteredRequests(filtered);
  };

  const handleSubmitRequest = async () => {
    if (!user) return;

    try {
      if (!newRequest.title.trim() || !newRequest.description.trim()) {
        toast.error('Please fill in the title and description');
        return;
      }

      const { error } = await supabase
        .from('custom_order_requests')
        .insert({
          user_id: user.id,
          title: newRequest.title.trim(),
          description: newRequest.description.trim(),
          budget_range: newRequest.budget_range || null,
          preferred_timeline: newRequest.preferred_timeline || null,
          aquarium_dimensions: newRequest.aquarium_dimensions || null,
          aquarium_type: newRequest.aquarium_type || null,
          special_requirements: newRequest.special_requirements || null,
          reference_images: newRequest.reference_images.length > 0 ? newRequest.reference_images : null,
          status: 'submitted'
        });

      if (error) throw error;

      toast.success('Custom request submitted successfully!');
      setShowNewRequestDialog(false);
      setNewRequest({
        title: '',
        description: '',
        budget_range: '',
        preferred_timeline: '',
        aquarium_dimensions: '',
        aquarium_type: '',
        special_requirements: '',
        reference_images: []
      });
      fetchRequests();
    } catch (error: any) {
      console.error('Error submitting request:', error);
      toast.error(`Failed to submit request: ${error.message}`);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      submitted: 'bg-blue-100 text-blue-800',
      under_review: 'bg-yellow-100 text-yellow-800',
      quote_provided: 'bg-purple-100 text-purple-800',
      approved: 'bg-green-100 text-green-800',
      in_progress: 'bg-indigo-100 text-indigo-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      low: 'bg-gray-100 text-gray-800',
      normal: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    return colors[priority] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'under_review':
        return <Clock className="h-4 w-4" />;
      case 'quote_provided':
        return <DollarSign className="h-4 w-4" />;
      case 'approved':
      case 'in_progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return <div className="text-center py-8">Loading your custom requests...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Custom Order Requests</h2>
          <p className="text-muted-foreground">
            Request custom aquarium decorations and track their progress
          </p>
        </div>
        <Dialog open={showNewRequestDialog} onOpenChange={setShowNewRequestDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Request
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Submit Custom Order Request</DialogTitle>
            </DialogHeader>
            <NewRequestForm 
              request={newRequest}
              setRequest={setNewRequest}
              onSubmit={handleSubmitRequest}
              onCancel={() => setShowNewRequestDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Requests</SelectItem>
              <SelectItem value="submitted">Submitted</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="quote_provided">Quote Provided</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.length > 0 ? (
          filteredRequests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-lg">{request.title}</CardTitle>
                      <Badge className={getStatusColor(request.status)}>
                        {getStatusIcon(request.status)}
                        <span className="ml-1">{request.status.replace('_', ' ')}</span>
                      </Badge>
                      <Badge className={getPriorityColor(request.priority)}>
                        {request.priority}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Submitted: {formatDate(request.created_at)}
                      </div>
                      {request.budget_range && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          Budget: {request.budget_range}
                        </div>
                      )}
                      {request.estimated_cost && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          Estimated: {formatCurrency(request.estimated_cost)}
                        </div>
                      )}
                    </div>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setSelectedRequest(request)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Request Details - {request.title}</DialogTitle>
                      </DialogHeader>
                      {selectedRequest && <RequestDetails request={selectedRequest} />}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                  {request.description}
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold mb-2">Project Details</h4>
                    <div className="space-y-1 text-sm">
                      {request.aquarium_type && (
                        <p><span className="font-medium">Type:</span> {request.aquarium_type}</p>
                      )}
                      {request.aquarium_dimensions && (
                        <p><span className="font-medium">Dimensions:</span> {request.aquarium_dimensions}</p>
                      )}
                      {request.preferred_timeline && (
                        <p><span className="font-medium">Timeline:</span> {request.preferred_timeline}</p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Status Information</h4>
                    <div className="space-y-1 text-sm">
                      {request.estimated_timeline && (
                        <p><span className="font-medium">Estimated Timeline:</span> {request.estimated_timeline}</p>
                      )}
                      {request.quote_expires_at && (
                        <p><span className="font-medium">Quote Expires:</span> {formatDate(request.quote_expires_at)}</p>
                      )}
                      {request.admin_notes && (
                        <p><span className="font-medium">Artist Notes:</span> {request.admin_notes}</p>
                      )}
                    </div>
                  </div>
                </div>

                {request.reference_images && request.reference_images.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Reference Images</h4>
                    <div className="flex gap-2 overflow-x-auto">
                      {request.reference_images.slice(0, 4).map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Reference ${index + 1}`}
                          className="w-20 h-20 object-cover rounded border"
                        />
                      ))}
                      {request.reference_images.length > 4 && (
                        <div className="w-20 h-20 bg-muted rounded border flex items-center justify-center text-xs">
                          +{request.reference_images.length - 4} more
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Plus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No custom requests yet</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'No requests match your current filters.' 
                  : 'Submit your first custom order request to get started.'}
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button onClick={() => setShowNewRequestDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Submit Request
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

// New Request Form Component
const NewRequestForm = ({ 
  request, 
  setRequest, 
  onSubmit, 
  onCancel 
}: {
  request: any;
  setRequest: (request: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
}) => {
  const handleImageUpload = (url: string) => {
    setRequest({
      ...request,
      reference_images: [...request.reference_images, url]
    });
  };

  const removeImage = (index: number) => {
    setRequest({
      ...request,
      reference_images: request.reference_images.filter((_: any, i: number) => i !== index)
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="title">Project Title *</Label>
          <Input
            id="title"
            value={request.title}
            onChange={(e) => setRequest({ ...request, title: e.target.value })}
            placeholder="e.g., Custom Coral Reef Sculpture"
            required
          />
        </div>

        <div>
          <Label htmlFor="description">Project Description *</Label>
          <Textarea
            id="description"
            value={request.description}
            onChange={(e) => setRequest({ ...request, description: e.target.value })}
            placeholder="Describe your vision, style preferences, and any specific requirements..."
            rows={4}
            required
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="aquarium_type">Aquarium Type</Label>
            <Select
              value={request.aquarium_type}
              onValueChange={(value) => setRequest({ ...request, aquarium_type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select aquarium type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="freshwater">Freshwater</SelectItem>
                <SelectItem value="saltwater">Saltwater</SelectItem>
                <SelectItem value="reef">Reef Tank</SelectItem>
                <SelectItem value="planted">Planted Tank</SelectItem>
                <SelectItem value="cichlid">Cichlid Tank</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="aquarium_dimensions">Aquarium Dimensions</Label>
            <Input
              id="aquarium_dimensions"
              value={request.aquarium_dimensions}
              onChange={(e) => setRequest({ ...request, aquarium_dimensions: e.target.value })}
              placeholder="e.g., 48&quot; x 18&quot; x 24&quot;"
            />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="budget_range">Budget Range</Label>
            <Select
              value={request.budget_range}
              onValueChange={(value) => setRequest({ ...request, budget_range: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select budget range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="under_500">Under $500</SelectItem>
                <SelectItem value="500_1000">$500 - $1,000</SelectItem>
                <SelectItem value="1000_2500">$1,000 - $2,500</SelectItem>
                <SelectItem value="2500_5000">$2,500 - $5,000</SelectItem>
                <SelectItem value="over_5000">Over $5,000</SelectItem>
                <SelectItem value="flexible">Flexible</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="preferred_timeline">Preferred Timeline</Label>
            <Select
              value={request.preferred_timeline}
              onValueChange={(value) => setRequest({ ...request, preferred_timeline: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select timeline" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asap">ASAP</SelectItem>
                <SelectItem value="1_2_weeks">1-2 weeks</SelectItem>
                <SelectItem value="3_4_weeks">3-4 weeks</SelectItem>
                <SelectItem value="1_2_months">1-2 months</SelectItem>
                <SelectItem value="flexible">Flexible</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="special_requirements">Special Requirements</Label>
          <Textarea
            id="special_requirements"
            value={request.special_requirements}
            onChange={(e) => setRequest({ ...request, special_requirements: e.target.value })}
            placeholder="Any special requirements, fish compatibility concerns, or installation needs..."
            rows={3}
          />
        </div>

        <div>
          <Label>Reference Images</Label>
          <div className="space-y-4">
            <ImageUpload
              value=""
              onChange={handleImageUpload}
              bucket="custom-requests"
            />
            {request.reference_images.length > 0 && (
              <div className="grid gap-2 grid-cols-4">
                {request.reference_images.map((image: string, index: number) => (
                  <div key={index} className="relative">
                    <img
                      src={image}
                      alt={`Reference ${index + 1}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0"
                      onClick={() => removeImage(index)}
                    >
                      ×
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onSubmit}>
          Submit Request
        </Button>
      </div>
    </div>
  );
};

// Request Details Component
const RequestDetails = ({ request }: { request: CustomOrderRequest }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Request Information */}
      <div className="grid gap-4 md:grid-cols-2">
        <div>
          <h3 className="font-semibold mb-2">Request Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Status:</span>
              <Badge className={request.status === 'completed' ? 'bg-green-100 text-green-800' : 
                request.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                'bg-yellow-100 text-yellow-800'}>
                {request.status.replace('_', ' ')}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Priority:</span>
              <Badge className={request.priority === 'urgent' ? 'bg-red-100 text-red-800' : 
                request.priority === 'high' ? 'bg-orange-100 text-orange-800' : 
                'bg-blue-100 text-blue-800'}>
                {request.priority}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Submitted:</span>
              <span>{formatDate(request.created_at)}</span>
            </div>
            <div className="flex justify-between">
              <span>Last Updated:</span>
              <span>{formatDate(request.updated_at)}</span>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Project Details</h3>
          <div className="space-y-2 text-sm">
            {request.aquarium_type && (
              <div className="flex justify-between">
                <span>Aquarium Type:</span>
                <span>{request.aquarium_type}</span>
              </div>
            )}
            {request.aquarium_dimensions && (
              <div className="flex justify-between">
                <span>Dimensions:</span>
                <span>{request.aquarium_dimensions}</span>
              </div>
            )}
            {request.budget_range && (
              <div className="flex justify-between">
                <span>Budget Range:</span>
                <span>{request.budget_range.replace('_', ' ')}</span>
              </div>
            )}
            {request.preferred_timeline && (
              <div className="flex justify-between">
                <span>Timeline:</span>
                <span>{request.preferred_timeline.replace('_', ' ')}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Description */}
      <div>
        <h3 className="font-semibold mb-2">Project Description</h3>
        <div className="p-3 bg-muted rounded-lg">
          <p className="text-sm whitespace-pre-wrap">{request.description}</p>
        </div>
      </div>

      {/* Special Requirements */}
      {request.special_requirements && (
        <div>
          <h3 className="font-semibold mb-2">Special Requirements</h3>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{request.special_requirements}</p>
          </div>
        </div>
      )}

      {/* Quote Information */}
      {(request.estimated_cost || request.estimated_timeline || request.quote_expires_at) && (
        <div>
          <h3 className="font-semibold mb-2">Quote Information</h3>
          <div className="p-3 bg-blue-50 rounded-lg space-y-2 text-sm">
            {request.estimated_cost && (
              <div className="flex justify-between">
                <span>Estimated Cost:</span>
                <span className="font-semibold">{formatCurrency(request.estimated_cost)}</span>
              </div>
            )}
            {request.estimated_timeline && (
              <div className="flex justify-between">
                <span>Estimated Timeline:</span>
                <span>{request.estimated_timeline}</span>
              </div>
            )}
            {request.quote_expires_at && (
              <div className="flex justify-between">
                <span>Quote Expires:</span>
                <span>{formatDate(request.quote_expires_at)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Artist Notes */}
      {request.admin_notes && (
        <div>
          <h3 className="font-semibold mb-2">Artist Notes</h3>
          <div className="p-3 bg-green-50 rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{request.admin_notes}</p>
          </div>
        </div>
      )}

      {/* Customer Notes */}
      {request.customer_notes && (
        <div>
          <h3 className="font-semibold mb-2">Your Notes</h3>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{request.customer_notes}</p>
          </div>
        </div>
      )}

      {/* Reference Images */}
      {request.reference_images && request.reference_images.length > 0 && (
        <div>
          <h3 className="font-semibold mb-2">Reference Images</h3>
          <div className="grid gap-4 grid-cols-2 md:grid-cols-3">
            {request.reference_images.map((image, index) => (
              <div key={index} className="relative">
                <img
                  src={image}
                  alt={`Reference ${index + 1}`}
                  className="w-full h-32 object-cover rounded border"
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
