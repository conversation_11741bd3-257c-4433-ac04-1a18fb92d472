// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ghvpvrwvsyjqrvqczdua.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdodnB2cnd2c3lqcXJ2cWN6ZHVhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4OTI5MTQsImV4cCI6MjA2ODQ2ODkxNH0.2AYOJCGGXGIsEpaOVqTSL-NXhJ9CAn9JmrccX23Er84";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});